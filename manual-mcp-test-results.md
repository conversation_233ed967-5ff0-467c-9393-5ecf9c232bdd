# Manual MCP Tool Test Results

## Tool: `roll_wod_pool`
### Test Case: Standard Roll
- **Input:** `{ "pool_size": 5, "difficulty": 6 }`
- **Expected Output:** Correct number of successes calculated.
- **Actual Output:**  
  ```
  Pool Size: 5, Difficulty: 6, Specialty: No
  Rolled: [10, 9, 9, 3, 5]
  ➡  Result: 3 successes
  [SUCCESS] Strong Success!
  Successes: 3
  ```
- **Result:** The system correctly counted 3 successes (all dice ≥6). Output is clear and matches expectations.
### Test Case: Specialty Rule
- **Input:** `{ "pool_size": 3, "difficulty": 6, "has_specialty": true }`
- **Expected Output:** Rolls of 10 should count as two successes when specialty is active.
- **Actual Outputs:**
  1. `Rolled: [7, 2, 6]` → 2 successes, no rolls of 10 observed.
  2. `Rolled: [9, 3, 7]` → 2 successes, no rolls of 10 observed.
- **Notes:** In both actual test runs (with specialty=true), output matched as expected for dice ≥6, but without a roll of 10 the specialty rule (double success for a 10) was not directly observed. Further targeted testing is recommended to confirm this rule, as a tool repetition limit was reached.

---

---
### Test Case: Zero/Negative Pool
- **Input 1:** `{ "pool_size": 0, "difficulty": 6 }`
- **Expected Output:** Rolls 1 "chance" die; only 10 is a success, 1 is a botch.
- **Actual Output:**  
  ```
  Pool Size: 0, Difficulty: 6, Specialty: No
  Rolled: [1]
  ➡  Result: 0 successes
  [BOTCH] Critical Botch! Catastrophic failure.
  ```
- **Result:** "Chance die" mechanic is correctly triggered. Rolling a 1 resulted in a botch, per WoD rules.

- **Input 2:** `{ "pool_size": -1, "difficulty": 6 }`
- **Expected Output:** Should return an error about invalid (negative) dice pool.
- **Actual Output:**  
  ```
  Pool Size: -1, Difficulty: 6, Specialty: No
  Rolled: [8]
  ➡  Result: 0 successes
  [FAILURE] Failure – No successes.
  ```
## Tool: `roll_contested_action`
### Test Case: Contested Logic
- **Input:**
  ```json
  {
    "attacker_pool": 5,
    "attacker_difficulty": 6,
    "attacker_specialty": false,
    "defender_pool": 3,
    "defender_difficulty": 6,
    "defender_specialty": false
  }
  ```
- **Expected Output:** Computes and announces winner by net successes, handles tie/botch resolution.
## Tool: `roll_soak`
### Test Case: Standard Soak (Lethal, no Fortitude)
- **Input:** `{ "soak_pool": 4, "damage_type": "lethal", "has_fortitude": false }`
- **Expected Output:** Number of dice >= 6 is reported as soak, with a summary narrative.
- **Actual Output:**
  ```
  Soak Dice: [7, 4, 4, 6] vs diff 6
  ➡  Soaked 2 points of damage.
  Solid soak effort.
### Test Case: Aggravated Soak with Fortitude
- **Input:** `{ "soak_pool": 3, "damage_type": "aggravated", "has_fortitude": true }`
- **Expected Output:** Allows soak for aggravated only with fortitude; increased difficulty. Number of dice >= diff is soak.
- **Actual Output:**
  ```
  Soak Dice: [7, 10, 6] vs diff 8
  ➡  Soaked 1 point of damage.
  Marginal soak – you reduce some, but not all, of the blow.
  ```
## Tool: `roll_damage_pool`
### Test Case: Standard Damage Pool Roll
- **Input:** `{ "pool_size": 4, "damage_type": "lethal" }`
- **Expected Output:** Successes reported as damage dealt (number of dice ≥ diff); includes summary narrative.
- **Actual Output:**  
  ```
  Tool execution failed:
  ZodError – invalid input (multiple schema/adapter errors).
  ```
## Tool: `roll_initiative_for_scene`
### Test Case: Standard Initiative Roll
- **Input:**
  ```json
  {
    "actors": [
      { "actor_name": "Alice", "initiative_pool": 6, "character_id": 1 },
      { "actor_name": "Bob", "initiative_pool": 5, "character_id": 2 },
      { "actor_name": "NPC Wolf", "initiative_pool": 7, "npc_id": 101 }
    ]
  }
  ```
## Tool: `roll_virtue_check`
### Test Case: Frenzy/Rötschreck (Self-Control)
- **Input:** `{ "character_id": 1, "virtue_name": "self-control", "difficulty": 8 }`
- **Expected Output:** Rolls pool, reports success/failure for resisting Frenzy or Rötschreck.
- **Actual Output:**  
  ```
  Tool execution failed:
  ZodError – invalid input (schema/adapter error).
  ```
# Summary and Observations

## Successful Manual Tests
- `roll_wod_pool` – Standard and specialty cases tested (specialty double not directly observed due to randomness/tool call limits).
- `roll_contested_action` – Contested resolution (including botch outcomes) validated as rules-compliant.
- `roll_soak` – Both standard and special ("aggravated" with fortitude) tested and passed.

## Blocked or Failing Tests
- `roll_damage_pool`, `roll_initiative_for_scene`, initiative/turn order chain, and all game-line-specific tools (`roll_virtue_check`, etc.) failed due to persistent backend/internal ZodError (schema/adapter) issues, not data validation errors.
- Example error: "ZodError – invalid input (schema/adapter error)."
- This prevents comprehensive manual validation; these failures should be addressed at the server/protocol layer before rerunning the full test suite.

## Recommendations
- Backend schema/adapter bugs must be fixed to enable testing of all MCP tool methods.
- Retest full plan (especially game-line-specific logic and initiative/turn management) after resolving ZodErrors and pool validation issues.
- **Notes:** Tool invocation failed due to internal error despite input matching schema. Unable to validate virtue tool for game-line testing.

---
- **Expected Output:** Returns a sorted turn order object, including initiative scores for each actor.
- **Actual Output:**
  ```
  Error: pool_size must be a non-negative integer
  ```
- **Notes:** All provided initiative_pool values were valid. The error prevented test execution; tool may be mishandling schema logic or input parsing. Unable to validate this and subsequent initiative/turn-management tools as a result.

---
- **Notes:** The tool invocation failed due to internal parsing/schema errors. Input matched the public tool schema. Unable to test or validate tool logic; possible bug in backend or protocol interface, blocking this test.

---
- **Result:** Matches expected rules logic for aggravated + fortitude; system acknowledged fortitude, applied higher difficulty, and returned correct result with narrative.

---
  ```
- **Result:** Matches expectations; 2 dice succeeded and the output is clear and informative. No discrepancies observed.

---
- **Actual Output:**
  ```
  Attacker: Pool 5 vs Diff 6 → Rolls: [10, 10, 4, 7, 3] (3 successes)
  Defender: Pool 3 vs Diff 6 → Rolls: [5, 3, 1] (0 successes) [BOTCH]
  RESULT: Defender BOTCHES! Attacker wins automatically.
  ```
- **Result:** Correct handling of contested roll; defender botch produces automatic win for attacker. Output is clear and rules-compliant.

---
- **Discrepancy:** Tool allowed negative dice pool instead of rejecting input. Test exposes a validation gap; negative pool_size should not be processed as a roll.

---
---