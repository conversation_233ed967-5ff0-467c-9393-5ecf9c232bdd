# MCP Server Test Plan – Issues and Debugging Report

This report consolidates all discovered issues, errors, and failed or ambiguous test cases found in the provided MCP server testing plans. For each issue, we provide context, symptoms, possible causes, and suggested debugging steps to streamline troubleshooting and future development.

---

## Table of Contents

1. [Game-Line-Specific Mechanics (Combat-Engine Server)](#game-line-specific-mechanics)
2. [Character & Antagonist Management (Game-State Server)](#character-antagonist-management)
3. [Resource, Health, & Progression (Game-State Server)](#resource-health-progression)
4. [Core Dice Mechanics & Turn Management (Combat-Engine Server)](#core-dice-mechanics)
5. [General Recommendations](#general-recommendations)

---

## Game-Line-Specific Mechanics

_Source file: `TestingPlan.GameLineSpecificMechanics.md`_

### Issues

#### 1. Changeling: Cantrip Botch/Narrative Output (Deterministic Testing)
- **Context:** Test: `invoke_cantrip` – "Banality Trigger"
- **Symptoms:** Test could not be validated. Tool does not provide explicit botch/narrative output, and results are non-deterministic—unable to force a botch for the test.
- **Possible Causes:**
  - Lack of a deterministic/test mode or seeding for dice rolls.
  - <PERSON>l does not output narrative guidance or explicit Banality triggers on botch results.
- **Debug Steps:**
  - Implement a test mode or optional random seed in MCP tools for forced outcome testing.
  - Add explicit botch handling and narrative output to the tool.
  - Ensure clear documentation for botch state outputs.

---

## Character & Antagonist Management

_Source file: `TestingPlan.CharacterManagement.md`_

### Issues

#### 2. Antagonist Creation: UNIQUE Constraint Failure

- **Context:** `create_antagonist` (Template: Sabbat Shovelhead / Name: Brutus)
- **Symptoms:** Fails with a UNIQUE constraint error on `(character_abilities.character_id, ability_name)`. Character is not created.
- **Possible Causes:**
  - Antagonist template or DB design flaw; duplicate ability assignments on creation.
  - Incorrect population of joined tables or missing uniqueness validation in code.
- **Debug Steps:**
  - Audit antagonist template population logic for duplicate abilities.
  - Check DB schema for proper UNIQUE index and handling of joins.
  - Add error handling and reporting for model-layer DB issues.

#### 3. Integration: Usability of Newly Created Character

- **Context:** After creating a character, immediately using `apply_damage`.
- **Symptoms:** Applying damage fails: "Missing damage_successes".
- **Possible Causes:**
  - Schema/tool mismatch: new character lacks required field(s) for immediate downstream use.
  - Documentation/usage not updated for latest input requirements.
- **Debug Steps:**
  - Update documentation for tool argument requirements (e.g., require "damage_successes").
  - Adjust client/test plan to fetch new character state and required args before subsequent calls.
  - Consider sensible defaults or clearer error messaging.

#### 4. Splat-Specific Update Not Supported

- **Context:** `update_character` called to change "humanity".
- **Symptoms:** Error: "no such column: humanity". Update fails.
- **Possible Causes:**
  - Trait is in a joined/per-splat table without update support in code.
  - Schema/migrations incomplete for joined tables.
- **Debug Steps:**
  - Add/update splat-specific update support in API/server.
  - Ensure migrations and joins are complete and align with business logic.
  - Update tests and docs after feature/fix.

---

## Resource, Health & Progression

_Source file: `TestingPlan.ResourceHealthProgression.md`_

### Issues

#### 5. Resource Gain: Blood Pool Not Configured

- **Context:** `gain_resource` on Lucien (vampire), attempting to gain "blood".
- **Symptoms:** Fails: "Character Lucien does not have a blood pool configured."
- **Possible Causes:**
  - Minimal character creation does not set up blood pool/max fields.
  - Mismatch between test plan and actual creation workflow defaults.
- **Debug Steps:**
  - Update character creation to set up resources for relevant splat (or improve error message).
  - Clarify in documentation/test plan setup preconditions.

#### 6. Damage Application: Missing/Invalid `damage_type` Param

- **Context:** `apply_damage` tool.
- **Symptoms:** Applying damage fails: "Missing or invalid damage_type".
- **Possible Causes:**
  - Test plan does not match actual tool schema.
  - Implementation missing required parameter validation.
- **Debug Steps:**
  - Align test plans with tool schemas.
  - Ensure all parameter validation is properly implemented.
  - Expand error messages to include expected schema info.

#### 7. Damage/Health Track Inadequate for Edge Cases

- **Context:** Health overflow/integration (incapacitation, wound penalties).
- **Symptoms:** Sections on health track overflow and wound penalties are marked as untested/incomplete.
- **Possible Causes:**
  - Server tools may lack logic for overflow, incapacitated state, or may not propagate wound penalties.
- **Debug Steps:**
  - Extend tool code for overflow/edge handling and tracking status.
  - Add integration with roll modifiers (penalties).
  - Ensure status is updated after each change and reflected in subsequent actions.

#### 8. XP Workflow: Unimplemented Scenarios

- **Context:** Award, spend, improve, and validation cases for XP and trait improvements.
- **Symptoms:** All XP/trait improvement tests are untested/unimplemented.
- **Possible Causes:**
  - Feature incomplete or test plans pending against server support.
- **Debug Steps:**
  - Implement or verify all required XP and trait improvement endpoints.
  - Add tests for each step and edge validation (cost calculation, sufficient XP, trait existence).

---

## Core Dice Mechanics & Turn Management

_Source file: `TestingPlan.CoreDiceMechanics.md`_

### Issues

#### 9. Zero/Negative Pool Handling and Param Mismatch

- **Context:** `roll_wod_pool` and related tests for edge case pools.
- **Symptoms:** Test fails: pool 0, missing/incorrect `difficulty` param; pool -1 correctly errors.
- **Possible Causes:**
  - Test plan missing required parameters.
  - Tool schema requires params not reflected in test.
  - Logic for chance die/scenario not fully implemented or specified.
- **Debug Steps:**
  - Revise test plan to ensure all required parameters are set.
  - Specify and document how zero/negative pool is to be handled (chance die/abort/error).
  - Update tool to give clear, user-friendly validation errors.

---

## General Recommendations

1. **Add Deterministic Test Modes:**  
   Enable seeding/random-overriding for dice/magick/etc. to allow forced test scenarios (for botches, successes, etc.).

2. **Align Test Plans and Tool Schemas:**  
   Audit every API and test for parameter/field mismatches; update both as needed.

3. **Improve Error and Narrative Outputs:**  
   All MCP tools should clearly state what went wrong and, on negative results (botches, overflows, etc.), supply a concise narrative/guidance string.

4. **Expand/Complete Edge Case Handling:**  
   Implement all missing logic for overflow, incapacitation, wound penalties, and cross-tool resource flows to enable full regression testing.

5. **Document and Communicate Required Preconditions:**  
   Update testing instructions and user manuals to spell out preconditions (e.g., resource pools, valid templates, etc.) for each test.

---

## Appendix: Summary Table of All Issues

| Area              | Issue                             | Symptom/Context                | Severity |
|-------------------|-----------------------------------|------------------------------- |----------|
| GameLine: Changeling | Botch/Narrative Handling       | Unable to test botch—no deterministic output or explicit narrative | Medium   |
| Character Mgmt    | Antagonist Creation              | UNIQUE constraint error        | High     |
| Character Mgmt    | Integration/Usability            | Downstream tool fails: missing args/schema mismatch | Medium |
| Character Mgmt    | Splat-Update                     | Cannot update splat-specific fields | Medium   |
| Resource/Health   | Resource Gain Config             | Missing blood pool setup       | Medium   |
| Resource/Health   | Damage Apply Schema              | Invalid/missing `damage_type`  | Medium   |
| Resource/Health   | Health Overflow & Penalties      | Untested: overflow/incapacitation/penalties | Medium |
| Resource/Health   | XP Flow and Validation           | Unimplemented                  | Medium   |
| Dice Mechanics    | Param/Edge Case Mismatch         | Schema vs. test misalign; missing param errors | Medium   |

---

**This report is intended to aid engineering, QA, and documentation teams in tracking, understanding, and prioritizing MCP server troubleshooting and future updates.**