# MCP Game-State Server: Resource, Health & Progression Testing Plan

---

**LLM Manual Execution Prompt:**

You are an LLM tasked with manually executing the following tests using the connected MCP game-state server. For each test:
- Run the described operation using the provided API/tool.
- Compare actual outcome to the expected output.
- Check the box ([x]) if the test passes; leave unchecked and provide notes if it fails.
- Complete all tests in each section before moving to the next section.

---

### Resource, Health, & Progression

#### `spend_resource` & `restore_resource`
- [x] **Standard Spend/Restore**
  Goal: Spend/restore a valid resource.
  Input: `{ "character_id": 5, "resource_name": "willpower", "amount": 1 }`
  Expected Output: Success message with new and max values (e.g., "Willpower: 4/5").
  > **Result:** ✅ Lucien spent 1 willpower. Remaining: 0.
- [x] **Validation: Insufficient**
  Goal: Prevent spending more than available.
  Input: Spend 10 Willpower when character has 0.
  Expected Output: Error: "Not enough willpower. Has 5, needs 10."
  > **Result:** Correct error: Not enough willpower! Current: 0, trying to spend: 10.
- [x] **Validation: Over-Restoring**
  Goal: Prevent restoring beyond the permanent maximum.
  Input: Restore 3 Willpower when at 0/1.
  Expected Output: Success message. New value is 1/1 (capped at max).
  > **Result:** Restored 1 willpower. Current: 1/1. Excess ignored/capped.
- [x] **Validation: Invalid Resource**
  Goal: Reject spending a resource the character doesn't have.
  Input: `spend_resource` with `resource_name: "blood"` on Mage character (Miles the Mage, ID 7).
  Expected Output: Error: "Invalid resource 'blood' for game_line 'mage'".
  > **Result:** Correct error: Resource 'blood' is not available for mage characters. Available resources: willpower, quintessence, paradox.

---

#### `gain_resource`
- [ ] **Standard Gain**
  Goal: Gain a resource from an action.
  Input: `{ "character_id": 5, "resource_name": "blood", "roll_successes": 3 }`
  Expected Output: Success message. Blood pool increases by 3 (up to max).
  > **Result:** ❌ Error: Character Lucien does not have a blood pool configured. Please set up the character's blood_pool_max field first. Not testable with minimal vampire creation; requires more complete setup or update.
- [x] **Validation: Invalid Resource**
  Goal: Reject gaining a resource not applicable to the game line.
  Input: Gain 'gnosis' for Lucien (Vampire, ID 5).
  Expected Output: Error message.
  > **Result:** Correct error: Resource 'gnosis' is not available for vampire characters. Available: willpower, blood.
- [x] **Validation: Non-Positive**
  Goal: Reject zero or negative successes.
  Input: `{ "character_id": 5, "resource_name": "blood", "roll_successes": 0 }`
  Expected Output: Error: "roll_successes must be a positive number."
  > **Result:** Correct error: Cannot gain blood for vampire or insufficient successes (need at least 1 success).

---

#### `apply_damage`
- [ ] **Damage Types**
  Goal: Verify Bashing, Lethal, and Aggravated damage apply correctly.
  Input: Apply 2 Bashing, then 1 Lethal to Lucien.
  Expected Output: Bashing upgrades to Lethal. Health track shows `X|X|X| | | |`.
  > **Result:** ❌ Error: Missing or invalid damage_type. Tool schema/input or implementation does not match checklist spec; cannot validate as written. Requires input schema alignment/fix.
- [ ] **Incapacitated/Overflow**
  Goal: Test damage that fills or exceeds the health track.
  Input: Apply 8 Lethal damage.
  Expected Output: Health track is full of 'X'. Status is Incapacitated.
- [ ] **Integration**
  Goal: Ensure wound penalties are reflected in subsequent rolls.
  Input: Apply 3 Lethal damage, then `roll_wod_pool`.
  Expected Output: A -1 wound penalty should be noted/applied to the roll.

---

#### `award_xp`, `spend_xp`, `improve_trait`, `get_trait_improvement_cost`
- [ ] **XP Flow**
  Goal: Award, check cost, improve, and verify new XP total.
  Input: `award_xp`, `get_trait_improvement_cost`, `improve_trait`.
  Expected Output: Each step succeeds. `get_character` shows increased trait and decreased XP.
- [ ] **Cost Calculation**
  Goal: Verify cost calculation is correct for all trait types.
  Input: `get_trait_improvement_cost` for Attribute, Ability, Discipline, etc.
  Expected Output: Correct costs returned (e.g., Attribute = new rating * 4).
- [ ] **Validation: Insufficient XP**
  Goal: Prevent improving a trait without enough XP.
  Input: `improve_trait` when XP is too low.
  Expected Output: Error: "Not enough XP."
- [ ] **Validation: Invalid Trait**
  Goal: Reject attempts to improve a nonexistent trait.
  Input: `improve_trait` with `trait_name: "Cooking"`.
  Expected Output: Error: "Trait 'Cooking' not found."