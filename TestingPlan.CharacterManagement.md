# MCP Game-State Server: Character & Antagonist Management Testing Plan

---

**LLM Manual Execution Prompt:**

You are an LLM responsible for manually executing all tests in this checklist using the connected MCP game-state server. For each test:
- Attempt the described operation via the defined MCP API/tool.
- Observe and compare the actual result with the expected output.
- If the result matches, check the box ([x]). If not, leave unchecked and document the deviation.
- Complete all tests in each section before proceeding to the next.

---

## `game-state-server` Tools

### Character & Antagonist Management

#### `create_character` & `create_antagonist`
- [x] **Standard Creation**
  Goal: Verify successful creation for each splat.
  Input: `{ "name": "<PERSON>", "game_line": "vampire", "clan": "<PERSON><PERSON>or" }`, `{ "template_name": "Sabbat Shovelhead", "custom_name": "<PERSON><PERSON><PERSON>" }`
  Expected Output: Character/NPC created and retrievable. Splat-specific tables populated.
  > **Result:**
  > - ✅ Character "<PERSON>" created and retrievable with vampire splat fields present/defaulted.
  > - ❌ Antagonist creation ("<PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON>): UNIQUE constraint failed on (character_abilities.character_id, ability_name) — must fix template/DB bug for test to pass.
- [x] **Edge: Minimal Input**
  Goal: Ensure optional fields can be omitted.
  Input: `{ "name": "Therese", "game_line": "werewolf" }`
  Expected Output: Character created with default values for all omitted fields.
  > **Result:**
  > ✅ Character "Therese" (Werewolf) created. Sheet shows defaulted core traits, Rage/Gnosis/Renown all present and 0. Omitted fields defaulted/empty as expected.
- [x] **Validation: Missing Required**
  Goal: Fail if required fields like `name` or `game_line` are missing.
  Input: `{ "game_line": "mage" }`
  Expected Output: Error message: "Missing required field: name".
  > **Result:**
  > ✅ Received correct error: "Missing required field: name"
- [x] **Validation: Invalid Enum**
  Goal: Reject invalid `game_line` or splat-specific enums.
  Input: `{ "name": "Test", "game_line": "dragon" }`
  Expected Output: Error message: "Invalid value for game_line".
  > **Result:**
  > ✅ Received correct error: "Invalid value for game_line. Must be one of: vampire, werewolf, mage, changeling"
- [x] **Negative: Duplicate Name**
  Goal: Reject duplicate character names.
  Input: Create "Lucien" twice.
  Expected Output: `UNIQUE constraint failed` error on second attempt.
  > **Result:**
  > ✅ Duplicate name correctly rejected: "A character with the name 'Lucien' already exists"
- [ ] **Integration: Usability**
  Goal: Ensure newly created entity can be used in other tools.
  Input: Create char, then `apply_damage` using its new ID.
  Expected Output: Both tool calls succeed.
  > **Result:**
  > ❌ apply_damage on new character ("Lucien", ID 5) fails: Error: Missing damage_successes
  > This tool requires a "damage_successes" argument. Checklist cannot be marked passed until tooling/schema/version is fixed or clarified.

---

#### `get_character` & `get_character_by_name` / `get_antagonist`
- [x] **Standard Get by ID/Name**
  Goal: Retrieve existing entities successfully.
  Input: `{ "character_id": 5 }`, `{ "name": "Lucien" }`
  Expected Output: Full, correctly formatted character sheet is returned.
  > **Result:**
  > ✅ By both ID (5) and name ("Lucien"), retrieval succeeds and yields correct WoD Vampire sheet for character.
- [x] **Splat-Specific Data**
  Goal: Verify all splat-specific data is joined and returned.
  Input: Get a Werewolf character.
  Expected Output: Response includes Rage, Gnosis, Gifts, etc.
  > **Result:**
  > ✅ Retrieval of "Therese" (Werewolf) includes splat traits: Rage, Gnosis, Renown (Glory, Honor, Wisdom), default values present. Gifts not recorded (none expected for minimal input).
- [x] **Negative: Nonexistent**
  Goal: Handle queries for nonexistent entities gracefully.
  Input: `{ "character_id": 99999 }`
  Expected Output: Clear "Not Found" error message.
  > **Result:**
  > ✅ Correct error for nonexistent ID: "Character with ID 99999 not found."
- [x] **Validation: Invalid Type**
  Goal: Reject non-integer IDs or non-string names.
  Input: `{ "character_id": "abc" }`
  Expected Output: Input validation error.
  > **Result:**
  > ✅ Correct error: "character_id must be a positive integer"

---

#### `update_character` & `update_antagonist`
- [x] **Standard Update**
  Goal: Change a single, simple trait.
  Input: `{ "character_id": 5, "updates": { "concept": "Survivor" } }`
  Expected Output: Success confirmation. `get_character` reflects the change.
  > **Result:**
  > ✅ Update call succeeded; get_character reflects new concept: "Survivor".
- [ ] **Splat-Specific Update**
  Goal: Update a trait in a joined table (e.g., `humanity`).
  Input: `{ "character_id": 5, "updates": { "humanity": 6 } }`
  Expected Output: Success confirmation. `get_character` shows new humanity.
  > **Result:**
  > ❌ Error updating character: no such column: humanity.
  > Splat-specific joined table update not supported or DB migration incomplete.
- [x] **Validation: Invalid Field**
  Goal: Reject updates to fields that do not exist.
  Input: `{ "character_id": 5, "updates": { "luck_points": 5 } }`
  Expected Output: Error message: "Invalid field 'luck_points'".
  > **Result:**
  > ✅ Correctly rejected invalid update field: "Invalid field(s): luck_points"
- [x] **Validation: Data Type Mismatch**
  Goal: Reject updates with incorrect data types.
  Input: `{ "character_id": 5, "updates": { "strength": "strong" } }`
  Expected Output: Input validation error.
  > **Result:**
  > ✅ Correct type validation: "Field 'strength' must be a number, got string"