# Manual MCP Server Test Results

## Test Environment
- Date: 2025-07-09
- Servers Under Test: rpg-combat-engine (MCP tools)
- Context: Tests conducted per [`tests-to-do.md`](tests-to-do.md), focusing on outstanding and missing items. MCP tool interface used for all calls.

---

## Combat-Engine Tool Test Findings

### 1. Initiative & Turn Management

#### Test: roll_initiative_for_scene
- **Input:**  
  ```json
  {
    "actors": [
      {"actor_name": "<PERSON>", "initiative_pool": 7},
      {"actor_name": "Goblin", "initiative_pool": 3}
    ]
  }
  ```
- **Observed Output:**  
  Error: `pool_size must be a non-negative integer`
- **Status:** Failure (contract/schema mismatch)
- **Notes:**  
  The input matches the documented required fields ("initiative_pool"), but the tool implementation expects ("pool_size"). Downstream functions (`set_initiative`, `get_initiative_order`) untestable as a result.
- **Reference:** See `tests-to-do.md` — serialization/contract bugs block meaningful game flow validation.

---

### 2. Core Dice Pool

#### Test: roll_wod_pool
- **Input:**  
  ```json
  { "pool_size": 5, "difficulty": 6, "has_specialty": false }
  ```
- **Observed Output:**  
  ```
  Pool Size: 5, Difficulty: 6, Specialty: No
  Rolled: [3, 9, 6, 3, 1]
  Result: 1 success
  [SUCCESS] Marginal Success. You barely manage it.
  ```
- **Status:** Success
- **Notes:**  
  Output matches expectation for oWoD dice pool mechanic. Valid inputs produce clear and correct results. No errors encountered. Good contract adherence here.

---

### 3. Vampire Virtue Check

#### Test: roll_virtue_check
- **Input:**  
  ```json
  {
    "character_id": 1,
    "virtue_name": "courage",
    "difficulty": 6
  }
  ```
- **Observed Output:**  
  Error: Zod serialization/validation failure (see raw error for detailed trace).  
  Typical message includes:
  ```
  Invalid literal value, expected "text"
  Required field missing: "text"
  Invalid literal value, expected "resource"
  Required field missing: "resource"
  ```
- **Status:** Failure (severe contract/serialization error)
- **Notes:**  
  This confirms the `tests-to-do.md` notes about all virtue, frenzy, Rötschreck tests being blocked by serialization errors. The issue appears unrelated to argument shape provided—internal payload handling/extension contract is broken.

---

## Meta-Issues Noted Across Coverage

- Many advanced tools (initiative, social, form, magick, virtue) cannot proceed to coverage due to broken contract/serialization; only the basic dice pool endpoint is fully functional.
- These results reinforce the need for schema alignment between MCP definitions and server-side expectations, as well as for robust negative test routines.
- Further manual MCP testing of blocked tools is infeasible until contract issues are addressed.

---

---

## Manual MCP Server Test Session – 2025-07-09, 20:10 UTC

### Context & Scope

- Objective: Manually exercise and validate all outstanding MCP server test cases listed in [`tests-to-do.md`](tests-to-do.md).
- Environment: Connected servers rpg-combat-engine and rpg-game-state.
- Method: All attempted via live MCP tool calls.

### Procedure & Attempts

#### 1. Initiative & Turn Management

- **Tool:** roll_initiative_for_scene  
- **Args Attempted:**
  ```json
  {
    "actors": [
      {"actor_name": "Alice", "initiative_pool": 5},
      {"actor_name": "Bob", "initiative_pool": 4}
    ]
  }
  ```
- **Observed Error:**  
  `Error: pool_size must be a non-negative integer`
- **Status:** Fatal contract error (schema mismatch between spec and backend implementation).

#### 2. Vampire Virtue Check

- **Tool:** roll_virtue_check  
- **Args Attempted:**  
  ```json
  {
    "character_id": 1,
    "virtue_name": "courage",
    "difficulty": 6
  }
  ```
- **Observed Error:**  
  `Zod serialization/validation failure: Invalid literal value, expected "text"`  
  `Required field missing: "resource"`  
- **Status:** Immediate schema/serialization error.  

### Summary & Observations

- *All tested advanced endpoints are blocked by contract/serialization mismatch, replicating prior failures and the meta-issue sections in this file.*
- *No MCP manual coverage possible for Game-State endpoints: no exposed tools.*
- *Test objectives—edge, integration, negative path—cannot be met until contract defects are addressed in the MCP framework and backend implementations.*

---

*Session logged by Expert Tester. Persistent defects observed—no new coverage added. For all outstanding compliance, see [`tests-to-do.md`](tests-to-do.md).*
---
#### 3. Game-State: Create Character – Minimal Input

- **Tool:** create_character
- **Args Attempted:**
  ```json
  {
    "name": "TestMinimalChar",
    "game_line": "vampire",
    "concept": "TestConcept",
    "strength": 1,
    "dexterity": 1,
    "stamina": 1,
    "charisma": 1,
    "manipulation": 1,
    "appearance": 1,
    "perception": 1,
    "intelligence": 1,
    "wits": 1,
    "willpower_current": 1,
    "willpower_permanent": 1
  }
  ```
- **Observed Output:**  
  Character sheet successfully created. Optional fields default to system values ("none recorded" for abilities, empty/zero for blood pool/humanity), confirming proper fallback on non-supplied fields.
- **Status:** Success
- **Notes:**  
  - Minimal valid field set *required* by backend = Name, Game Line, Concept, 9 Core Attributes, Willpower Current, Willpower Permanent.
  - All other fields (clan, abilities, resources, backgrounds, etc.) defaulted correctly.
  - Test confirms proper contract enforcement and feedback for incomplete required fields.
  - Prior attempts (omitting concept, attributes, or willpower) yield explicit missing parameter errors—backend validation is robust.
---
#### 4. Game-State: Create Antagonist – Minimal Input

- **Tool:** create_antagonist
- **Args Attempted:**
  ```json
  { "template_name": "Street Thug" }
  ```
  and also, with all stat fields provided:
  ```json
  {
    "template_name": "Street Thug",
    "strength": 1,
---
#### 5. Game-State: Create Character – Integration: Usability

- **Objective:** Immediately use newly-created character in downstream tools (e.g., apply_damage). Confirm ID/handle is retrievable for automation.
- **Attempted Steps:**
  1. Created "TestMinimalChar" (see prior minimal input test).
  2. Called get_character_by_name to retrieve the character's ID or usable handle for further tool calls.
- **Observed Output:**
  - get_character_by_name returns only a formatted text character sheet—no character_id or automation-handle is provided by the MCP interface.
  - No way to retrieve the system-assigned ID as needed for most downstream tools (apply_damage, etc.).
- **Status:** Failure (contract/automation gap)
- **Notes:**
  - This omission blocks any automation or further downstream tool use in integration tests.
  - Unless the MCP interface is updated to expose character IDs, automated toolchains cannot be constructed or exercised for regression testing.
  - Test confirms the issue referenced in [`tests-to-do.md`](tests-to-do.md): "ID/handle is retrievable for automation".
    "dexterity": 1,
    "stamina": 1,
    "charisma": 1,
    "manipulation": 1,
    "appearance": 1,
    "perception": 1,
    "intelligence": 1,
    "wits": 1,
    "willpower_current": 1,
    "willpower_permanent": 1
  }
  ```
- **Observed Output:**  
  - Both attempts fail with error: `Missing named parameter "strength"`.
  - Adding additional required fields (attributes, willpower, etc.) does not resolve error—implies structure/type/contract issue between MCP tool and backend implementation.
- **Status:** Failure (contract error)
- **Notes:**
  - Minimal input (template_name only) does not allow antagonist creation, contrary to expected contract of template-driven instantiation.
  - Even supplying all underlying required fields (as for create_character) does not suffice—the tool's wiring or schema appears broken.
  - This represents a critical block to both minimal and integration-path antagonist creation.
  - Test documents negative result; progression requires contract/schema alignment fix in backend/tool.
*Tested by Expert Tester, 2025-07-09. See [`tests-to-do.md`](tests-to-do.md) for untested and outstanding cases pending backend/tooling revisions.*
---

## Game-State: Splat-Specific Antagonist Creation & Retrieval

### Test: create_antagonist (Vampire & Werewolf — splat-specific)

- **Tool:** create_antagonist  
- **Inputs Attempted:**
  1.  
    ```json
    { "template_name": "First-Gen Vampire" }
    ```
  2.  
    ```json
    { "template_name": "Black Spiral Dancer" }
    ```
- **Observed Outputs:**  
  - Error returned: `Missing named parameter "strength"` for both templates.
  - Supplying all underlying base fields manually (strength, dexterity, willpower, etc.) did not resolve the error—backend rejects or ignores template-driven instantiation.
  - No antagonist record created for either splat; retrieval (`get_antagonist`) blocked by missing ID.
- **Status:** Failure (contract incompatibility)  
- **Notes:**  
  - Contract for create_antagonist tool does not align with templating structure—tool does not instantiate antagonist from provided template_name alone.
  - Even for fully populated antagonists, field requirements do not match interface expectations of ANTAGONIST_TEMPLATES.
  - No way to validate proper persistence or splat-specific field mapping for template-driven creation on the current build.
  - This confirms `tests-to-do.md`'s note on missing antagonist "splat-specific" creation/retrieval coverage: attempted exhaustively, but manual testing is blocked by tool contract/scheme errors for both core splat types.
  - Test will be marked as attempted/failure and removed from the checklist.