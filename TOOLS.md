# MCP Server Tools & API Reference

This document provides a complete reference for all Model Context Protocol (MCP) tools available in the World of Darkness server suite. These tools are the building blocks for all game mechanics, character management, and chronicle progression.

## 📁 Server Architecture

The system uses a two-server model:

*   **`game-state-server`**: Manages persistent data, including character sheets, NPC records, inventory, experience, and world state. It is the "source of truth" for the chronicle.
*   **`combat-engine-server`**: A stateless server that handles the game's dice mechanics and rule adjudications, such as rolling dice pools, resolving contested actions, and applying game-line-specific rules (e.g., Frenzy, Magick, Cantrips).

---

## 🗄️ `game-state-server` Tools

This server handles the "state" of your characters and the world.

### Character Management

#### `create_character`
Creates a new character with core attributes and splat-specific traits.

**Input Schema:**
```json
{
  "name": "Character Name",
  "game_line": "vampire" | "werewolf" | "mage" | "changeling",
  "concept": "Character Concept (optional)",
  // --- Core Attributes (defaults to 1 if not provided) ---
  "strength": 2, "dexterity": 3, "stamina": 2,
  "charisma": 3, "manipulation": 4, "appearance": 2,
  "perception": 3, "intelligence": 2, "wits": 3,
  // --- Core Traits ---
  "willpower_current": 5, "willpower_permanent": 5,
  // --- Splat-Specific Fields (provide based on game_line) ---
  "clan": "Malkavian", "generation": 12, "humanity": 7, // (Vampire)
  "tribe": "Glass Walkers", "auspice": "Ragabash", // (Werewolf)
  "tradition_convention": "Verbena", "arete": 3, // (Mage)
  "kith": "Pooka", "seeming": "Wilder", // (Changeling)
  // --- Relational Traits (optional) ---
  "abilities": [ { "name": "Firearms", "type": "skills", "rating": 2 } ],
  "disciplines": [ { "name": "Auspex", "rating": 1 } ]
}
```
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "🎲 World of Darkness: VAMPIRE Sheet\n\n👤 Name: Marcus\n🧠 Concept: Rebel with a cause\n...\n(Full formatted character sheet)"
    }
  ]
}
```

---
#### `get_character` / `get_character_by_name`
Retrieves a full, formatted character sheet by ID or name.

**Input Schema:**
```json
{ "character_id": 1 }
// or
{ "name": "Marcus" }
```
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "🎲 World of Darkness: VAMPIRE Sheet\n\n👤 Name: Marcus\n...(Full formatted character sheet)"
    }
  ]
}
```

---
#### `update_character`
Modifies a character's core or splat-specific traits.

**Input Schema:**
```json
{
  "character_id": 1,
  "updates": {
    "willpower_current": 4,
    "concept": "Hardened Survivor"
  }
}
```
**Example Response:**
```json
{
  "content": [ { "type": "text", "text": "✅ Character #1 updated." } ]
}
```

---
#### `list_characters`
Lists all player characters in the database.

**Input Schema:** `{}`
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "🎭 Character Roster:\n- Marcus (vampire) [ID: 1]\n- Cries-at-the-Moon (werewolf) [ID: 2]"
    }
  ]
}
```

### Resource & Health Management

#### `spend_resource` / `restore_resource`
Spends or restores a character's resource pool (e.g., Willpower, Blood).

**Input Schema:**
```json
{
  "character_id": 1,
  "resource_name": "willpower" | "blood" | "gnosis" | "rage" | "glamour" | "quintessence",
  "amount": 1
}
```
**Example Response (`spend_resource`):**
```json
{
  "content": [
    { "type": "text", "text": "Marcus spent 1 willpower. Remaining: 4" },
    { "type": "object", "tool_outputs": { "success": true, "resource_spent": "willpower", "amount_spent": 1, "remaining": 4 } }
  ]
}
```

---
#### `gain_resource`
Gains a resource from an in-game action (e.g., feeding, meditation).

**Input Schema:**
```json
{
  "character_id": 1,
  "resource_name": "blood",
  "roll_successes": 3
}
```
**Example Response:**
```json
{
  "content": [
    { "type": "text", "text": "🩸 Marcus fed and gained 3 Blood.\nBlood Pool: 8/10" },
    { "type": "object", "resource": "blood", "gained": 3, "new_total": 8, "character_id": 1 }
  ]
}
```

---
#### `apply_damage`
Applies damage to a target's health levels.

**Input Schema:**
```json
{
  "target_type": "character" | "npc",
  "target_id": 1,
  "damage_successes": 3,
  "damage_type": "lethal" | "bashing" | "aggravated"
}
```
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "💥 Damage applied. Health: /|X|X|X| | |  | Penalty: -1"
    }
  ]
}
```

### XP & Progression

#### `award_xp` / `spend_xp`
Awards or spends character experience points.

**Input Schema:**
```json
{
  "character_id": 1,
  "amount": 5,
  "reason": "Completed the 'Missing Ghoul' story arc."
}
```
**Example Response (`award_xp`):**
```json
{
  "content": [
    { "type": "text", "text": "✅ Awarded 5 XP to 'Marcus'. Reason: Completed the 'Missing Ghoul' story arc.\n\nTotal XP: 12" }
  ]
}
```
---
#### `improve_trait`
Spends XP to increase a character's trait. Automatically calculates cost and validates XP.

**Input Schema:**
```json
{
  "character_id": 1,
  "trait_type": "attribute" | "ability" | "discipline" | "sphere" | "art" | "realm" | "willpower",
  "trait_name": "strength"
}
```
**Example Response:**
```json
{
  "content": [
    { "type": "text", "text": "🌟 TRAIT IMPROVED! 🌟\n\n👤 Character: Marcus\n- Trait: ATTRIBUTE - strength\n- Old Rating: 2\n+ New Rating: 3\n- XP Cost: 12 (Rule: New rating × 4)\n+ Remaining XP: 0" },
    { "type": "object", "char_name": "Marcus", "spent": 12, "trait_type": "attribute", "trait_name": "strength", "previous_rating": 2, "new_rating": 3, "xp_formula": "New rating × 4", "remaining_xp": 0 }
  ]
}
```
---
### Status Effects
#### `apply_status_effect` / `remove_status_effect` / `get_status_effects`
Manages temporary or long-term conditions affecting a character or NPC.

**Input Schema (`apply_status_effect`):**
```json
{
  "target_type": "character" | "npc",
  "target_id": 1,
  "effect_name": "Stunned",
  "description": "Cannot act this round.",
  "mechanical_effect": { "can_act": false },
  "duration_type": "rounds",
  "duration_value": 1
}
```
**Example Response (`apply_status_effect`):**
```json
{
  "content": [
    { "type": "text", "text": "🌀 Status effect 'Stunned' applied to character #1 (ID: 101)" },
    { "type": "object", "effect_id": 101, "target_type": "character", "target_id": 1, "effect_name": "Stunned", "duration_type": "rounds", "duration_value": 1 }
  ]
}
```

---

## ⚔️ `combat-engine-server` Tools

This server handles stateless dice rolls and rule adjudications.

### Core Dice Mechanics

#### `roll_wod_pool`
The primary tool for all actions. Rolls a pool of d10s and calculates successes.

**Input Schema:**
```json
{
  "pool_size": 5,
  "difficulty": 6,
  "has_specialty": false
}
```
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "🎲 oWoD Dice Pool Roll\n\nPool Size: 5, Difficulty: 6, Specialty: No\nRolled: [7, 3, 1, 9, 10]\n➡  Result: 2 successes\n[SUCCESS] Moderate Success."
    }
  ]
}
```

---
#### `roll_contested_action`
Rolls for two actors and determines the winner based on net successes.

**Input Schema:**
```json
{
  "attacker_pool": 6, "attacker_difficulty": 6, "attacker_specialty": true,
  "defender_pool": 5, "defender_difficulty": 7, "defender_specialty": false
}
```
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "🎯 CONTESTED/RESISTED ACTION\n\nAttacker: Pool 6 vs Diff 6 → Rolls: [10,10,8,4,2,1] (4 successes)\nDefender: Pool 5 vs Diff 7 → Rolls: [9,3,1,5,8] (2 successes)\n\nRESULT: Attacker wins by 2 net successes."
    }
  ]
}
```

---
#### `roll_soak`
Rolls a soak pool to reduce incoming damage.

**Input Schema:**
```json
{
  "soak_pool": 4,
  "damage_type": "lethal",
  "has_fortitude": false
}
```
**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "Soak Dice: [8, 2, 9, 6] vs diff 6\n➡  Soaked 3 points of damage.\nSolid soak effort."
    }
  ]
}
```

---
#### `roll_damage_pool`
Rolls a damage pool after a successful attack to determine health levels dealt.

**Input Schema:**
```json
{
  "pool_size": 5,
  "damage_type": "lethal"
}
```
**Example Response:**
```json
{
  "content": [
    { "type": "text", "text": "💥 Damage Pool Roll\n\nPool Size: 5, Difficulty: 6\nDamage Type: Lethal\nRolled: [7, 4, 8, 1, 9]\n➡  Result: 2 levels of lethal damage." },
    { "type": "object", "data": { "successes": 2, "damage_type": "lethal" } }
  ]
}
```

### Game-Line Specific Mechanics

#### `roll_virtue_check` (Vampire)
Rolls for virtues like Courage, Self-Control, or Conscience.

**Input Schema:**
```json
{
  "character_id": 1,
  "virtue_name": "Courage",
  "difficulty": 7
}
```

---
#### `change_form` (Werewolf)
Returns the attribute modifiers for a Werewolf changing forms.

**Input Schema:**
```json
{
  "character_id": 2,
  "target_form": "Crinos"
}
```

---
#### `roll_magick_effect` (Mage)
Rolls an Arete pool for a magickal effect and calculates any Paradox.

**Input Schema:**
```json
{
  "character_id": 3,
  "spheres": ["Forces", "Life"],
  "arete_roll_pool": 4,
  "difficulty": 8,
  "is_coincidental": false
}
```

---
#### `invoke_cantrip` (Changeling)
Rolls a pool of Art + Realm for a cantrip.

**Input Schema:**
```json
{
  "character_id": 4,
  "art_pool": 3,
  "realm_pool": 2,
  "difficulty": 7
}
```