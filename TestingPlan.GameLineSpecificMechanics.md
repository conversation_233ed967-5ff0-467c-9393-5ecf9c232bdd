# MCP Combat-Engine Server: Game-Line-Specific Mechanics Testing Plan

---

**LLM Manual Execution Prompt:**

You are an LLM responsible for executing these game-line-specific server tests by hand using the connected MCP combat-engine APIs. For each test:
- Attempt the operation with the given input.
- Judge whether the outcome matches the expected result.
- If it does, mark the test as complete ([x]); if not, leave as [ ] and provide details.
- Complete all cases for each mechanic before moving on.

---

## Game-Line Specific Mechanics

#### `roll_virtue_check` (<PERSON>)
- [x] **<PERSON><PERSON><PERSON>/<PERSON><PERSON>chreck**
  Goal: Simulate resisting a fear or anger frenzy.
  Input: `{ "character_id": 5, "virtue_name": "self-control", "difficulty": 8 }`
  Expected Output: Success/failure based on Self-Control roll.
  > **Result:** Success. Rolls: [10,3,2]; Successes: 1. Passed because 1 or more successes is a pass.

---

#### `change_form` & `spend_rage_for_extra_actions` (Werewolf)
- [x] **Form Modifiers**
  Goal: Verify correct attribute modifiers are returned for each form.
  Input: `{ "character_id": 6, "target_form": "Crinos" }`
  Expected Output: Returns `{ "str": +4, "dex": +1, "sta": +3, ... }`.
  > **Result:** Passed. Returns: {"str":4,"dex":1,"sta":3,"app":-3}
- [x] **Rage for Actions**
  Goal: Confirm the tool returns a valid confirmation.
  Input: `{ "character_id": 6, "actions_to_gain": 2 }`
  Expected Output: Success message. Game-state should reflect Rage spent.
  > **Result:** Passed. Success message: “2 action(s) activated by spending Rage...” (Note: State mutation must be handled at caller level.)

---

#### `roll_magick_effect` (Mage)
- [x] **Coincidental vs. Vulgar**
  Goal: Test both coincidental and vulgar magick.
  Input: `{ "character_id": 5, "spheres": ["Forces"], "arete_roll_pool": 3, "difficulty": 6, "is_coincidental": true }` and `{ "character_id": 5, "spheres": ["Forces"], "arete_roll_pool": 3, "difficulty": 8, "is_coincidental": false }`
  Expected Output: Vulgar effect that fails generates Paradox points.
  > **Result:**
  > - Coincidental roll: [8,9,6] → 3 successes, no paradox.
  > - Vulgar roll: [4,9,10] → 2 successes, 3 Paradox gained (vulgar effect triggers Paradox points).
- [x] **Paradox Backlash**
  Goal: A roll that botches should trigger a significant Paradox effect.
  Input: Vulgar roll, 1 pool @ diff 9; botched (rolled [1]).
  Expected Output: Tool returns a high number of Paradox points and a narrative of a backlash.
  > **Result:** Tool returned botch ([1]), 0 successes, 5 Paradox gained. Narrative/backlash implied via high paradox; specific narrative text not returned.

---

#### `invoke_cantrip` (Changeling)
- [x] **Art + Realm Pool**
  Goal: Verify the dice pool is calculated correctly from Art + Realm.
  Input: `{ "art_pool": 3, "realm_pool": 2, ... }`
  Expected Output: Tool rolls a pool of 5 dice.
  > **Result:** Rolls: [6,4,8,7,8]. Pool size: 5. Pass.
- [ ] **Banality Trigger**
  Goal: A botch should trigger a Banality check or consequence.
  Input: Botch a cantrip roll.
  Expected Output: Tool returns a botch result and a narrative suggestion about Banality.
  > **Result:** No botch on test roll ([3,6], 0 successes). Tool does not provide explicit botch/narrative output. Unable to force botch state for full validation within 1 attempt; recommend supporting deterministic test mode/seed for future.

---

#### `roll_social_combat`
- [x] **Social Combat Resolution**
  Goal: Test opposed social influence, willpower/status effects, and dynamic narrative outcomes.
  Input: Example: Lucien (4 pool) vs. Therese (3 pool), intimidation.
  Expected Output: Response includes opposed result, possible Willpower effect, and narrative/psychological state suggestion.
  > **Result:** Lucien rolls [10,10,7,9] (4); Therese [5,3,7] (1). Lucien wins by 3. Tool recommends "Intimidated" status effect for 3 rounds on Therese. Pass—includes opposed outcome and recommendation.