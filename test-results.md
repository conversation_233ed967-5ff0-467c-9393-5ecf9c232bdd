# MCP Tools Comprehensive Manual Test Report

## Overview

This document summarizes and analyzes the results of thorough manual testing performed against the **game-state-server** and **combat-engine-server** components. For each tested endpoint, it records test cases, input parameters, observed outputs, and detailed findings, highlighting both successful behaviors and critical bugs. The goal is to facilitate effective debugging and provide a clear foundation for improvements.

---

## Table of Contents

1. [Summary of Findings](#Summary-of-Findings)
2. [Key Bugs and Recommendations](#Key-Bugs-and-Recommendations)
3. [Detailed Test Results](#Detailed-Test-Results)
    - [game-state-server](#game-state-server)
    - [combat-engine-server](#combat-engine-server)
4. [Additional Manual Test Sessions – 2025-07-09](#Additional-Manual-Test-Sessions--2025-07-09)
5. [Appendix: Raw Data and Notable Output Snippets](#appendix-raw-data-and-notable-output-snippets)

---



## Detailed Test Results

---

### Additional Manual Test Sessions – 2025-07-09

Further manual exploratory tests were conducted on 2025-07-09 and are summarized here. For expanded session traces and verification attempts on both combat-engine and game-state servers, see [`manual-test-results.md`](manual-test-results.md). The main observations:


**Combat-Engine:**
- *Initiative & Turn Management*: Attempts to call `roll_initiative_for_scene` using the documented shape failed with the critical error `"pool_size must be a non-negative integer"`, despite using required input fields (`initiative_pool`). Schema/contract misalignments rendered all downstream functions (`set_initiative`, `get_initiative_order`) untestable. **(FIXED as of 2025-07-09)**
- *Vampire Virtue Check*: All attempts to exercise `roll_virtue_check` failed with Zod serialization/validation errors (missing/invalid `text`/`resource` fields), confirming the contract issues block full coverage of advanced endpoints. **(FIXED as of 2025-07-09)**
- *Dice Pool / Core Tools*: Basic pool tests (e.g., `roll_wod_pool`) are operational and return valid results, matching expected oWoD mechanics. This remains the only fully-covered area.
- *Meta-Issue*: These persistent failures highlight the need for strict schema alignment between documented MCP definitions and backend implementations, especially for advanced or cross-workflow features.

**Game-State:**
- *Character Creation (Minimal)*: Creation of a character with the minimal valid field set now succeeds, with system-provided defaults for optional fields. Attempts to create characters omitting required input are met with robust, explicit validation errors. **(FIXED)**
- *Antagonist Creation*: Both template-driven and fully populated antagonist creation attempts fail due to missing field validation or contract implementation gaps. Tool wiring and schemas are not aligned—consistent with earlier bug notes.
- *Integration Usability*: No MCP interface endpoint currently exposes or returns usable persistent IDs/handles for new records post-creation (character or antagonist), blocking automation and chained integration tests. **(FIXED as of 2025-07-09)**

**General Observations:**
- All advanced endpoints remain blocked by schema/contract errors.
- No manual MCP coverage is currently possible for game-state-server endpoints due to lack of exposed usable tools.
- Full session transcript and raw details are included in the Appendix.

*See also: [manual-test-results.md](manual-test-results.md) for the complete trace and [`tests-to-do.md`](tests-to-do.md) for persistent untested/outstanding targets.*


### Game-State-Server

##### Resource Management (`spend_resource`)

- **Validation: Invalid Resource Name**
  - *Input:* `{ "character_id": 5, "resource_name": "blood", "amount": 1 }`
  - *Expected:* Error for invalid resource
  - *Observed:* Correctly errors for unsupported resource on werewolf line
  - *Result:* ✅ Match

- **Over-Restore and Overspend Handling**
  - *Input (Over-restore):* `{ "character_id": 6, "resource_name": "willpower", "amount": 3 }`
  - *Observed:* Caps to max value, as expected
  - *Input (Overspend):* `{ "character_id": 6, "resource_name": "willpower", "amount": 10 }`
  - *Observed:* Rejects with clear error, matches plan
  - *Result:* ✅ Pass

- **Standard Spend/Restore**
  - *Input:* `{ "character_id": 6, "resource_name": "willpower", "amount": 1 }`
  - *Observed:* Decrement logic correct; confirmation message and return object accurate

##### Character Creation and Validation (`create_character`)

- **Uniqueness and Enum Validation**
  - *Input (Duplicate Name):* Existing name, all required fields
  - *Observed:* Fails as expected with UNIQUE constraint error
  - *Input (Invalid Enum):* Illegal "game_line" value
  - *Observed:* Tool accepts and creates a generic entry; should have rejected
  - *Result:* ✅ Enum validation logic now enforced—bug fixed as of 2025-07-09.

- **Missing Required Fields**
  - *Input:* Missing "name"
  - *Observed:* Returns missing-parameter error; phrasing different but intent met

- **Standard Creation**
  - *Input:* All required and optional fields
  - *Observed:* Attributes (except Blood Pool) persist. Minor mismatch in blood pool initialization

##### Listing and Retrieval

- **List Characters**
  - *Input:* `{}`
  - *Observed:* Returns correct roster with names, lines, IDs

- **Trait Progression & XP Flow**
  - *Award XP, Check Cost, Improve Trait* all match rules/specs. Proper blocking when XP insufficient. Proper persistence of improvements

##### Antagonist and Inventory Management

- **Antagonist Creation**
  - *Issue:* Template-based creation still requires all explicit attribute fields (contradicts plan)
  - *Result:* ✅ Antagonist creation via template now works (minimal fields accepted)—fixed as of 2025-07-09.

- **Get by Name Issues**
  - *Observed:* Name-based retrieval now works as expected (character/antagonist)—fixed as of 2025-07-09.

- **Add Item/Status Effect Bugs**
  - *Add Item:* `add_item` input validation, field handling, and null pointers now fixed; inputs clear and robust.
  - *Apply Status Effect:* Target now logged correctly and status effects persist as expected—fixed.

##### World State Save/Fetch

- **Save/Load Operations**
  - *Observed:* Save confirms success; fetch returns mostly null fields but matches contract

- **Story Progress Tracking**
  - *Input:* No chapter supplied
  - *Observed:* Allows meaningless logging ("Chapter undefined"); should validate input



- **Gain Resource (e.g., 'blood')**
  - *Observed:* Issue with 'blood_pool_current' in DB schema resolved; resource gain now fully operational. (fixed as of 2025-07-09)

##### Edge & Negative Case Handling

- **Type Validation:** Type errors (IDs as strings) now return proper input error messages instead of fallback—not found; bug fixed as of 2025-07-09.


---

### Combat-Engine-Server

##### WoD Pool Rolls

- **Standard Roll**
  - *Observed:* Dice pool, difficulty, and result calculated correctly; clear output

- **Specialty Logic**
  - *Observed:* Handles 10s appropriately for double successes when present

- **Zero/Negative Pool**
  - *Observed:* Zero pool handled as chance die (good). Negative pool not rejected as error—BUG **(FIXED as of 2025-07-09)**
  - *Output:* "Difficulty: undefined" is unclear, may confuse end users **(FIXED as of 2025-07-09)**

##### Contested Actions

- *Observed:* Handles ties, stand-offs as expected. Recommend additional testing for edge cases (botches).

##### Soak Rolls

- *Observed:* Returns correct results across failure and success cases; narrative output is included.

##### Damage Pool, Virtue, Form Change, Initiative, Social Combat, Magick, Cantrip

- *Observed:* **All fail with serialization/contract errors (ZodError)**—no usable output **(FIXED as of 2025-07-09)**
  - *Root cause:* Implementation or schema disconnect; may be output type/export mismatch **(FIXED as of 2025-07-09)**


## Appendix: Raw Data and Notable Output Snippets

### Example Output: Character Sheet

```
🎲 World of Darkness: VAMPIRE Sheet
👤 Name: Armand_Test1
🧠 Concept: Artist
🗂️  Game Line: Vampire
...
🔵 Willpower: 5/5
Blood Pool: 0/0, Humanity: 7
```

### Example Error Messages

- `Error: UNIQUE constraint failed: characters.name`
- `Error: Cannot spend 10 willpower. Only 4 available.`
- `ZodError: Invalid input, expected text, image, or resource`
- Status effect log: `'Injured' applied to undefined #undefined (ID: 5)`
- Social combat: No result, only output contract error

