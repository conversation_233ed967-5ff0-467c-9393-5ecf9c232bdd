"use strict";
// File: game-state-server/src/health-tracker.ts
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthTracker = void 0;
var HEALTH_LEVELS = [
    'bruised',
    'hurt',
    'injured',
    'wounded',
    'mauled',
    'crippled',
    'incapacitated'
];
var PENALTIES = {
    bruised: 0,
    hurt: -1,
    injured: -1,
    wounded: -2,
    mauled: -2,
    crippled: -5,
    incapacitated: 0
};
var DAMAGE_SYMBOL = {
    bashing: '/',
    lethal: 'X',
    aggravated: '*'
};
var HealthTracker = /** @class */ (function () {
    /**
     * Initializes with a JSON or record describing the current health boxes.
     * Accepts both V20 object and count formats. Handles corrupted state robustly.
     */
    function HealthTracker(health) {
        if (health === void 0) { health = undefined; }
        this.health = health;
        this.boxes = Array(7).fill('');
        this.deserializeBoxArray(health);
    }
    HealthTracker.prototype.fallbackFullHealth = function () {
        this.boxes = Array(7).fill('');
    };
    /**
     * Accepts legacy/modern JSON, string, or nothing; parses to 7-boxes.
     */
    HealthTracker.prototype.deserializeBoxArray = function (source) {
        var healthObj;
        try {
            if (typeof source === 'string') {
                healthObj = JSON.parse(source !== null && source !== void 0 ? source : '{}');
            }
            else if (typeof source === 'object' && source) {
                healthObj = source;
            }
            else {
                throw new Error();
            }
            if (typeof healthObj !== 'object' || healthObj === null)
                throw new Error();
        }
        catch (e) {
            healthObj = HEALTH_LEVELS.reduce(function (acc, lvl) {
                acc[lvl] = {};
                return acc;
            }, {});
        }
        // preferred fill-in per box: support V20 {b:1,l:0,a:0} or just number (count of filled damage)
        var out = [];
        for (var _i = 0, HEALTH_LEVELS_1 = HEALTH_LEVELS; _i < HEALTH_LEVELS_1.length; _i++) {
            var lvl = HEALTH_LEVELS_1[_i];
            var boxVal = healthObj[lvl];
            if (typeof boxVal === 'object' && boxVal !== null) {
                // V20 style: {b:1,l:0,a:0}
                if (boxVal.a > 0)
                    out.push('*');
                else if (boxVal.l > 0)
                    out.push('X');
                else if (boxVal.b > 0)
                    out.push('/');
                else
                    out.push('');
            }
            else if (typeof boxVal === 'number') {
                // Simple number: count of filled boxes, no type
                out.push(boxVal > 0 ? '/' : '');
            }
            else {
                out.push('');
            }
        }
        // If corrupt, fallback
        if (out.length !== HEALTH_LEVELS.length || out.some(function (x) { return typeof x !== 'string' || x.length > 1; })) {
            this.fallbackFullHealth();
        }
        else {
            this.boxes = out;
        }
    };
    /**
     * Returns simple JSON health object (V20 style, e.g. {bruised: {b:1}, ...})
     */
    HealthTracker.prototype.toJSON = function () {
        var out = {};
        for (var i = 0; i < HEALTH_LEVELS.length; ++i) {
            var symbol = this.boxes[i];
            if (symbol === '*')
                out[HEALTH_LEVELS[i]] = { a: 1 };
            else if (symbol === 'X')
                out[HEALTH_LEVELS[i]] = { l: 1 };
            else if (symbol === '/')
                out[HEALTH_LEVELS[i]] = { b: 1 };
            else
                out[HEALTH_LEVELS[i]] = {};
        }
        return out;
    };
    /**
     * Returns printable visual status: e.g. "/|*|/|X|...|"
     */
    HealthTracker.prototype.getBoxArray = function () {
        return __spreadArray([], this.boxes, true);
    };
    /** Returns wound penalty for current state according to most severe filled box. */
    HealthTracker.prototype.getWoundPenalty = function () {
        for (var i = this.boxes.length - 1; i >= 0; --i) {
            if (this.boxes[i] !== '') {
                return PENALTIES[HEALTH_LEVELS[i]];
            }
        }
        return 0;
    };
    /** Applies any combination of bashing, lethal, aggravated (any falsy is 0). Returns {changed: bool}. */
    HealthTracker.prototype.applyDamage = function (dmg) {
        var _this = this;
        var orig = this.getBoxArray().join('');
        // Application order: aggravated > lethal > bashing
        var applyType = function (count, symbol) {
            for (var i = 0; i < (count || 0); ++i) {
                // aggravated: first '', then upgrade '/' or 'X' to '*'
                // lethal: first '', then upgrade '/' to 'X'
                // bashing: first '', only
                var idx = -1;
                if (symbol === '*') {
                    idx = _this.boxes.findIndex(function (x) { return x === '' || x === '/' || x === 'X'; });
                }
                else if (symbol === 'X') {
                    idx = _this.boxes.findIndex(function (x) { return x === '' || x === '/'; });
                }
                else if (symbol === '/') {
                    idx = _this.boxes.findIndex(function (x) { return x === ''; });
                }
                if (idx !== -1) {
                    // Upgrading existing
                    if (_this.boxes[idx] === '' ||
                        (symbol === 'X' && _this.boxes[idx] === '/') ||
                        (symbol === '*' && (_this.boxes[idx] === '/' || _this.boxes[idx] === 'X'))) {
                        _this.boxes[idx] = symbol;
                    }
                }
            }
        };
        applyType(dmg.aggravated || 0, '*');
        applyType(dmg.lethal || 0, 'X');
        applyType(dmg.bashing || 0, '/');
        // overflow: if >7, last become aggravated
        var over = this.boxes.filter(function (c) { return c === '*' || c === 'X' || c === '/'; }).length - 7;
        if (over > 0) {
            for (var i = this.boxes.length - 1; i >= 0 && over > 0; --i) {
                if (this.boxes[i] !== '*') {
                    this.boxes[i] = '*';
                    over--;
                }
            }
        }
        return this.getBoxArray().join('') !== orig;
    };
    /**
     * Serializes to JSON-string.
     */
    HealthTracker.prototype.serialize = function () {
        return JSON.stringify(this.toJSON());
    };
    /**
     * Static: build from DB (object or JSON-string) and always get a valid instance.
     */
    HealthTracker.from = function (source) {
        return new HealthTracker(source);
    };
    /**
     * Static: returns a fully healthy instance.
     */
    HealthTracker.healthy = function () {
        return new HealthTracker();
    };
    return HealthTracker;
}());
exports.HealthTracker = HealthTracker;
