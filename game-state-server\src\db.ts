// File: game-state-server/src/db.ts

import Database from 'better-sqlite3';
import { existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { ANTAGONIST_TEMPLATES } from './antagonists.js';
import { HealthTracker, DamageObject } from './health-tracker.js';

// --- Interface Definitions ---
interface CharacterRow {
  [key: string]: any; // Allow dynamic access for trait improvements, etc.
  id: number;
  name: string;
  concept?: string | null;
  game_line: string;
  strength: number; dexterity: number; stamina: number;
  charisma: number; manipulation: number; appearance: number;
  perception: number; intelligence: number; wits: number;
  willpower_current: number; willpower_permanent: number;
  health_levels: string; // JSON
  blood_pool_current?: number;
  // ... and all other game-line specific fields
}

export interface AntagonistRow {
  id: number;
  name: string;
  template: string;
  concept: string;
  game_line: string;
  strength: number;
  dexterity: number;
  stamina: number;
  charisma: number;
  manipulation: number;
  appearance: number;
  perception: number;
  intelligence: number;
  wits: number;
  willpower_current: number;
  willpower_permanent: number;
  health_levels: string;
  blood_pool_current: number;
  notes: string;
}

export interface NpcRow {
  id: number;
  name: string;
  template: string;
  concept: string;
  game_line: string;
  strength: number;
  dexterity: number;
  stamina: number;
  charisma: number;
  manipulation: number;
  appearance: number;
  perception: number;
  intelligence: number;
  wits: number;
  willpower_current: number;
  willpower_permanent: number;
  health_levels: string;
  blood_pool_current: number;
  notes: string;
}

// Create data directory in workspace
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const DATA_DIR = join(__dirname, '..', '..', 'data');
if (!existsSync(DATA_DIR)) {
  mkdirSync(DATA_DIR, { recursive: true });
}
const DB_PATH = join(DATA_DIR, 'game-state.db');

export class GameDatabase {
  private db: Database.Database;

  constructor() {
    this.db = new Database(DB_PATH);
    this.db.pragma('journal_mode = WAL');
    this.initializeSchema();
  }

  private initializeSchema() {
    // --- oWoD-centric Core Character Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS characters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        concept TEXT,
        game_line TEXT NOT NULL,
        -- Core Attributes
        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,
        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,
        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,
        -- Core Traits
        willpower_current INTEGER DEFAULT 1,
        willpower_permanent INTEGER DEFAULT 1,
        health_levels TEXT NOT NULL -- JSON
      );
    `);

    // --- Relational Tables ---
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_abilities (character_id INTEGER, ability_name TEXT, ability_type TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, ability_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_disciplines (character_id INTEGER, discipline_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, discipline_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_arts (character_id INTEGER, art_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, art_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_realms (character_id INTEGER, realm_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, realm_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_gifts (character_id INTEGER, gift_name TEXT, rank INTEGER, PRIMARY KEY(character_id, gift_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_spheres (character_id INTEGER, sphere_name TEXT, rating INTEGER, specialty TEXT, PRIMARY KEY(character_id, sphere_name), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);
    this.db.exec(`CREATE TABLE IF NOT EXISTS character_derangements (id INTEGER PRIMARY KEY, character_id INTEGER, derangement TEXT, description TEXT, UNIQUE(character_id, derangement), FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE);`);

    // --- Inventory Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER NOT NULL,
        item_name TEXT NOT NULL,
        item_type TEXT, -- e.g., 'Weapon', 'Trinket', 'Consumable'
        quantity INTEGER DEFAULT 1,
        description TEXT,
        properties TEXT, -- JSON for stats like weapon damage, etc.
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );
    `);

    // --- World & Story Persistence Tables ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS world_state (
        id INTEGER PRIMARY KEY, -- Use a single row for the whole campaign for simplicity
        location TEXT,
        notes TEXT,
        data TEXT, -- Flexible JSON blob for NPCs, events, etc.
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    this.db.exec(`
      CREATE TABLE IF NOT EXISTS story_progress (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chapter INTEGER,
        scene TEXT,
        summary TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // --- Game-line Specific Trait Tables (modular) ---
    // Vampire
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_vampire_traits (
        character_id INTEGER PRIMARY KEY,
        clan TEXT,
        generation INTEGER,
        blood_pool_current INTEGER,
        blood_pool_max INTEGER,
        humanity INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Werewolf
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_werewolf_traits (
        character_id INTEGER PRIMARY KEY,
        breed TEXT,
        auspice TEXT,
        tribe TEXT,
        gnosis_current INTEGER,
        gnosis_permanent INTEGER,
        rage_current INTEGER,
        rage_permanent INTEGER,
        renown_glory INTEGER,
        renown_honor INTEGER,
        renown_wisdom INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Mage
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_mage_traits (
        character_id INTEGER PRIMARY KEY,
        tradition_convention TEXT,
        arete INTEGER,
        quintessence INTEGER,
        paradox INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);
    // Changeling
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_changeling_traits (
        character_id INTEGER PRIMARY KEY,
        kith TEXT,
        seeming TEXT,
        glamour_current INTEGER,
        glamour_permanent INTEGER,
        banality_permanent INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );`);

    // ADDITION: Experience Ledger table for character XP transactions
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS experience_ledger (
        id INTEGER PRIMARY KEY,
        character_id INTEGER NOT NULL,
        amount INTEGER NOT NULL,
        reason TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
      );
    `);

    // --- Refactored Modular Antagonists/NPCs Table ---
    this.db.exec(`DROP TABLE IF EXISTS npcs;`); // Backup data first!
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npcs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        template TEXT,
        concept TEXT,
        game_line TEXT NOT NULL,
        strength INTEGER DEFAULT 1, dexterity INTEGER DEFAULT 1, stamina INTEGER DEFAULT 1,
        charisma INTEGER DEFAULT 1, manipulation INTEGER DEFAULT 1, appearance INTEGER DEFAULT 1,
        perception INTEGER DEFAULT 1, intelligence INTEGER DEFAULT 1, wits INTEGER DEFAULT 1,
        willpower_current INTEGER DEFAULT 1,
        willpower_permanent INTEGER DEFAULT 1,
        health_levels TEXT NOT NULL, -- JSON
        notes TEXT
      );
    `);
    // Modular splat trait tables for NPCs -- structure mirrors player traits
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_vampire_traits (
        npc_id INTEGER PRIMARY KEY,
        clan TEXT,
        generation INTEGER,
        blood_pool_current INTEGER,
        blood_pool_max INTEGER,
        humanity INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_werewolf_traits (
        npc_id INTEGER PRIMARY KEY,
        breed TEXT,
        auspice TEXT,
        tribe TEXT,
        gnosis_current INTEGER,
        gnosis_permanent INTEGER,
        rage_current INTEGER,
        rage_permanent INTEGER,
        renown_glory INTEGER,
        renown_honor INTEGER,
        renown_wisdom INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_mage_traits (
        npc_id INTEGER PRIMARY KEY,
        tradition_convention TEXT,
        arete INTEGER,
        quintessence INTEGER,
        paradox INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS npc_changeling_traits (
        npc_id INTEGER PRIMARY KEY,
        kith TEXT,
        seeming TEXT,
        glamour_current INTEGER,
        glamour_permanent INTEGER,
        banality_permanent INTEGER,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );`);

    // --- Initiative Tracking Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS initiative_order (
        scene_id TEXT NOT NULL,
        character_id INTEGER,
        npc_id INTEGER,
        actor_name TEXT NOT NULL,
        initiative_score INTEGER NOT NULL,
        turn_order INTEGER NOT NULL,
        PRIMARY KEY(scene_id, turn_order),
        FOREIGN KEY(character_id) REFERENCES characters(id) ON DELETE SET NULL,
        FOREIGN KEY(npc_id) REFERENCES npcs(id) ON DELETE SET NULL
      );
    `);
      // --- Turn Management Table for Combat Scenes ---
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS scenes (
          scene_id TEXT PRIMARY KEY,
          current_round INTEGER NOT NULL DEFAULT 1,
          current_turn_order INTEGER NOT NULL DEFAULT 0
        );
      `);
    // --- Generic Status Effects Table ---
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS status_effects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER,
        npc_id INTEGER,
        effect_name TEXT NOT NULL,
        description TEXT,
        mechanical_effect TEXT,
        duration_type TEXT DEFAULT 'indefinite',
        duration_value INTEGER,
        FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
        FOREIGN KEY (npc_id) REFERENCES npcs(id) ON DELETE CASCADE
      );
    `);
  }

  createCharacter(data: any) {
    if (!['vampire', 'werewolf', 'mage', 'changeling'].includes(data.game_line)) {
      throw new Error(`Invalid game_line: ${data.game_line}. Must be one of: vampire, werewolf, mage, changeling`);
    }

    const health_levels = data.health_levels || { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
    let charId: number | undefined = undefined;

    // Transactional logic: all sub-table inserts are done atomically
    charId = this.db.transaction(() => {
      let localCharId: number;
      // Insert core character data
      const stmt = this.db.prepare(`
        INSERT INTO characters (
          name, concept, game_line,
          strength, dexterity, stamina, charisma, manipulation, appearance,
          perception, intelligence, wits,
          willpower_current, willpower_permanent, health_levels
        ) VALUES (
          @name, @concept, @game_line,
          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,
          @perception, @intelligence, @wits,
          @willpower_current, @willpower_permanent, @health_levels
        )
      `);

      const result = stmt.run({
        name: data.name,
        concept: data.concept || null,
        game_line: data.game_line,
        strength: data.strength || 1,
        dexterity: data.dexterity || 1,
        stamina: data.stamina || 1,
        charisma: data.charisma || 1,
        manipulation: data.manipulation || 1,
        appearance: data.appearance || 1,
        perception: data.perception || 1,
        intelligence: data.intelligence || 1,
        wits: data.wits || 1,
        willpower_current: data.willpower_current || 1,
        willpower_permanent: data.willpower_permanent || 1,
        health_levels: JSON.stringify(health_levels)
      });
      localCharId = result.lastInsertRowid as number;

      // --- Insert into game-line-specific tables ---

      switch (data.game_line) {
        case 'vampire':
          this.db.prepare(`
            INSERT INTO character_vampire_traits
            (character_id, clan, generation, blood_pool_current, blood_pool_max, humanity)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.clan ?? null, data.generation ?? null,
            data.blood_pool_current ?? null, data.blood_pool_max ?? null,
            data.humanity ?? null
          );
          break;
        case 'werewolf':
          this.db.prepare(`
            INSERT INTO character_werewolf_traits
            (character_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.breed ?? null, data.auspice ?? null, data.tribe ?? null,
            data.gnosis_current ?? null, data.gnosis_permanent ?? null,
            data.rage_current ?? null, data.rage_permanent ?? null,
            data.renown_glory ?? null, data.renown_honor ?? null, data.renown_wisdom ?? null
          );
          break;
        case 'mage':
          this.db.prepare(`
            INSERT INTO character_mage_traits
            (character_id, tradition_convention, arete, quintessence, paradox)
            VALUES (?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.tradition_convention ?? null,
            data.arete ?? null,
            data.quintessence ?? null,
            data.paradox ?? null
          );
          break;
        case 'changeling':
          this.db.prepare(`
            INSERT INTO character_changeling_traits
            (character_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            localCharId,
            data.kith ?? null, data.seeming ?? null,
            data.glamour_current ?? null, data.glamour_permanent ?? null,
            data.banality_permanent ?? null
          );
          break;
        // Additional splats can be added here in similar fashion
      }

      // Changeling-specific: arts/reals
      if (data.game_line === "changeling") {
        if (data.arts && Array.isArray(data.arts)) {
          const artStmt = this.db.prepare(
            `INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)`
          );
          for (const a of data.arts) {
            artStmt.run(localCharId, a.art_name ?? a.name ?? a.label ?? '', Number(a.rating) || 0);
          }
        }
        if (data.realms && Array.isArray(data.realms)) {
          const realmStmt = this.db.prepare(
            `INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)`
          );
          for (const r of data.realms) {
            realmStmt.run(localCharId, r.realm_name ?? r.name ?? r.label ?? '', Number(r.rating) || 0);
          }
        }
      }

      // Example sub-table transactional inserts; expand for all relations as needed
      if (data.abilities && Array.isArray(data.abilities)) {
        const abilityStmt = this.db.prepare(
          `INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty)
           VALUES (?, ?, ?, ?, ?)`
        );
        for (const ability of data.abilities) {
          abilityStmt.run(localCharId, ability.name, ability.type, ability.rating, ability.specialty ?? null);
        }
      }
      if (data.disciplines && Array.isArray(data.disciplines)) {
        const discStmt = this.db.prepare(
          `INSERT INTO character_disciplines (character_id, discipline_name, rating)
           VALUES (?, ?, ?)`
        );
        for (const d of data.disciplines) {
          discStmt.run(localCharId, d.name, d.rating);
        }
      }
      // ... perform additional transactional inserts for arts, realms, gifts, etc., as needed
      return localCharId;
    })();

    return this.getCharacterById(charId!);
  }
    
  createAntagonist(template_name: string, custom_name?: string) {
    const template = (ANTAGONIST_TEMPLATES as any)[template_name];
    if (!template) return null;
    // Fill missing health_levels from default if template omits it
    const defaultHealthLevels = { bruised: 0, hurt: 0, injured: 0, wounded: 0, mauled: 0, crippled: 0, incapacitated: 0 };
    const data = {
      ...template,
      name: custom_name || template.name || template_name,
      template: template_name,
      health_levels: template.health_levels ?? defaultHealthLevels
    };
    let npcId: number | undefined = undefined;

    // Validate required fields after filling health_levels
    if (!data.name || !data.game_line || !data.health_levels) {
      console.error("Missing required fields in antagonist template:", template_name, data);
      return null;
    }

    
    // Transaction to insert core NPC and relational data
    this.db.transaction(() => {
      // 1. Insert into new lean core npcs table (no game-line-specific splat traits here)
      const stmt = this.db.prepare(`
        INSERT INTO npcs (
          name, template, concept, game_line,
          strength, dexterity, stamina, charisma, manipulation, appearance,
          perception, intelligence, wits,
          willpower_current, willpower_permanent, health_levels, notes
        ) VALUES (
          @name, @template, @concept, @game_line,
          @strength, @dexterity, @stamina, @charisma, @manipulation, @appearance,
          @perception, @intelligence, @wits,
          @willpower_current, @willpower_permanent, @health_levels, @notes
        )
      `);
      const result = stmt.run({
        name: data.name,
        template: data.template,
        concept: data.concept || null,
        game_line: data.game_line,
        strength: data.strength || 1,
        dexterity: data.dexterity || 1,
        stamina: data.stamina || 1,
        charisma: data.charisma || 1,
        manipulation: data.manipulation || 1,
        appearance: data.appearance || 1,
        perception: data.perception || 1,
        intelligence: data.intelligence || 1,
        wits: data.wits || 1,
        willpower_current: data.willpower_current || 1,
        willpower_permanent: data.willpower_permanent || 1,
        health_levels: JSON.stringify(data.health_levels ?? {}),
        notes: data.notes || null
      });
      npcId = result.lastInsertRowid as number;
      // 2. Modular splat trait tables
      switch (template.game_line) {
        case 'vampire':
          this.db.prepare(`
            INSERT INTO npc_vampire_traits
            (npc_id, clan, generation, blood_pool_current, blood_pool_max, humanity)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.clan ?? null,
            template.generation ?? null,
            template.blood_pool_current ?? null,
            template.blood_pool_max ?? null,
            template.humanity ?? null
          );
          break;
        case 'werewolf':
          this.db.prepare(`
            INSERT INTO npc_werewolf_traits
            (npc_id, breed, auspice, tribe, gnosis_current, gnosis_permanent, rage_current, rage_permanent, renown_glory, renown_honor, renown_wisdom)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.breed ?? null,
            template.auspice ?? null,
            template.tribe ?? null,
            template.gnosis_current ?? null,
            template.gnosis_permanent ?? null,
            template.rage_current ?? null,
            template.rage_permanent ?? null,
            template.renown_glory ?? null,
            template.renown_honor ?? null,
            template.renown_wisdom ?? null
          );
          break;
        case 'mage':
          this.db.prepare(`
            INSERT INTO npc_mage_traits
            (npc_id, tradition_convention, arete, quintessence, paradox)
            VALUES (?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.tradition_convention ?? null,
            template.arete ?? null,
            template.quintessence ?? null,
            template.paradox ?? null
          );
          break;
        case 'changeling':
          this.db.prepare(`
            INSERT INTO npc_changeling_traits
            (npc_id, kith, seeming, glamour_current, glamour_permanent, banality_permanent)
            VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            npcId,
            template.kith ?? null,
            template.seeming ?? null,
            template.glamour_current ?? null,
            template.glamour_permanent ?? null,
            template.banality_permanent ?? null
          );
          break;
        // Expand for other splats as needed
      }

      // 3. Relational data (abilities, disciplines, gifts, spheres, arts, realms)
      if (template.abilities) {
        const abilities = template.abilities;
        const abilityStmt = this.db.prepare(`INSERT INTO character_abilities (character_id, ability_name, ability_type, rating, specialty) VALUES (?, ?, ?, ?, NULL)`);
        if (abilities.talents) {
          for (const [name, rating] of Object.entries(abilities.talents)) {
            abilityStmt.run(npcId, name, 'Talent', rating);
          }
        }
        if (abilities.skills) {
          for (const [name, rating] of Object.entries(abilities.skills)) {
            abilityStmt.run(npcId, name, 'Skill', rating);
          }
        }
        if (template.supernatural?.disciplines) {
          const discStmt = this.db.prepare(`INSERT INTO character_disciplines (character_id, discipline_name, rating) VALUES (?, ?, ?)`);
          for (const [name, rating] of Object.entries(template.supernatural.disciplines)) {
            discStmt.run(npcId, name, rating);
          }
        }
        if (template.supernatural?.gifts) {
          const giftStmt = this.db.prepare(`INSERT INTO character_gifts (character_id, gift_name, rank) VALUES (?, ?, ?)`);
          for (const [name, rank] of Object.entries(template.supernatural.gifts)) {
            giftStmt.run(npcId, name, rank);
          }
        }
        if (template.supernatural?.spheres) {
          const sphStmt = this.db.prepare(`INSERT INTO character_spheres (character_id, sphere_name, rating) VALUES (?, ?, ?)`);
          for (const [name, rating] of Object.entries(template.supernatural.spheres)) {
            sphStmt.run(npcId, name, rating);
          }
        }
        if (template.supernatural?.arts) {
          const artStmt = this.db.prepare(`INSERT INTO character_arts (character_id, art_name, rating) VALUES (?, ?, ?)`);
          for (const [name, rating] of Object.entries(template.supernatural.arts)) {
            artStmt.run(npcId, name, rating);
          }
        }
        if (template.supernatural?.realms) {
          const realmStmt = this.db.prepare(`INSERT INTO character_realms (character_id, realm_name, rating) VALUES (?, ?, ?)`);
          for (const [name, rating] of Object.entries(template.supernatural.realms)) {
            realmStmt.run(npcId, name, rating);
          }
        }
        // ... Add more relational logic for new splats here if needed
      }
    })();

    return this.getAntagonistById(npcId!);
  }

  removeStatusEffect(effect_id: number): boolean {
    return true;
  }

  listStatusEffects(target_type: string, target_id: number): any[] {
    if (!target_type || !target_id) return [];
    const col = target_type === "character"
      ? "character_id"
      : target_type === "npc"
      ? "npc_id"
      : null;
    if (!col) return [];
    return this.db.prepare(
      `SELECT * FROM status_effects WHERE ${col} = ?`
    ).all(target_id);
  }

  getCharacterByName(name: string): CharacterRow | null {
    const row = this.db.prepare('SELECT * FROM characters WHERE name = ?').get(name);
    return row ? (row as CharacterRow) : null;
  }

  getAntagonistByName(name: string): AntagonistRow | null {
    const row = this.db.prepare('SELECT * FROM npcs WHERE name = ?').get(name);
    return row ? (row as AntagonistRow) : null;
  }

  getAntagonistById(id: number): AntagonistRow | null {
    const row = this.db.prepare('SELECT * FROM npcs WHERE id = ?').get(id);
    return row ? (row as AntagonistRow) : null;
  }

  getCharacterById(id: number): CharacterRow | null {
    const row = this.db.prepare('SELECT * FROM characters WHERE id = ?').get(id);
    return row ? (row as CharacterRow) : null;
  }

  updateCharacter(character_id: number, updates: any): void {
    const validKeys = [
      'name', 'concept', 'game_line', 'strength', 'dexterity', 'stamina',
      'charisma', 'manipulation', 'appearance', 'perception', 'intelligence', 'wits',
      'willpower_current', 'willpower_permanent', 'health_levels', 'experience',
      // Vampire fields
      'clan', 'generation', 'blood_pool_current', 'blood_pool_max', 'humanity',
      // Werewolf fields
      'tribe', 'auspice', 'rage_current', 'rage_permanent', 'gnosis_current', 'gnosis_permanent',
      // Mage fields
      'tradition', 'essence', 'arete', 'quintessence', 'paradox',
      // Changeling fields
      'kith', 'court', 'glamour_current', 'glamour_permanent', 'banality'
    ];

    const scalarFields = Object.keys(updates).filter(k => validKeys.includes(k));

    this.db.transaction(() => {
      // Update main character table
      if (scalarFields.length > 0) {
        const values = scalarFields.map(f =>
          (f === 'health_levels' && typeof updates[f] === 'object')
            ? JSON.stringify(updates[f])
            : updates[f]
        );
        const setClause = scalarFields.map(f => `\`${f}\` = ?`).join(', ');
        this.db.prepare(`UPDATE characters SET ${setClause} WHERE id = ?`).run(...values, character_id);
      }

      // Update splat-specific tables if needed
      const char = this.getCharacterById(character_id);
      if (char) {
        switch (char.game_line) {
          case 'vampire':
            const vampireFields = ['clan', 'generation', 'blood_pool_current', 'blood_pool_max', 'humanity'];
            const vampireUpdates = Object.keys(updates).filter(k => vampireFields.includes(k));
            if (vampireUpdates.length > 0) {
              const vampireValues = vampireUpdates.map(f => updates[f]);
              const vampireSetClause = vampireUpdates.map(f => `${f} = ?`).join(', ');
              this.db.prepare(`UPDATE character_vampire_traits SET ${vampireSetClause} WHERE character_id = ?`)
                .run(...vampireValues, character_id);
            }
            break;

          case 'werewolf':
            const werewolfFields = ['tribe', 'auspice', 'rage_current', 'rage_permanent', 'gnosis_current', 'gnosis_permanent'];
            const werewolfUpdates = Object.keys(updates).filter(k => werewolfFields.includes(k));
            if (werewolfUpdates.length > 0) {
              const werewolfValues = werewolfUpdates.map(f => updates[f]);
              const werewolfSetClause = werewolfUpdates.map(f => `${f} = ?`).join(', ');
              this.db.prepare(`UPDATE character_werewolf_traits SET ${werewolfSetClause} WHERE character_id = ?`)
                .run(...werewolfValues, character_id);
            }
            break;

          case 'mage':
            const mageFields = ['tradition', 'essence', 'arete', 'quintessence', 'paradox'];
            const mageUpdates = Object.keys(updates).filter(k => mageFields.includes(k));
            if (mageUpdates.length > 0) {
              const mageValues = mageUpdates.map(f => updates[f]);
              const mageSetClause = mageUpdates.map(f => `${f} = ?`).join(', ');
              this.db.prepare(`UPDATE character_mage_traits SET ${mageSetClause} WHERE character_id = ?`)
                .run(...mageValues, character_id);
            }
            break;

          case 'changeling':
            const changelingFields = ['kith', 'court', 'glamour_current', 'glamour_permanent', 'banality'];
            const changelingUpdates = Object.keys(updates).filter(k => changelingFields.includes(k));
            if (changelingUpdates.length > 0) {
              const changelingValues = changelingUpdates.map(f => updates[f]);
              const changelingSetClause = changelingUpdates.map(f => `${f} = ?`).join(', ');
              this.db.prepare(`UPDATE character_changeling_traits SET ${changelingSetClause} WHERE character_id = ?`)
                .run(...changelingValues, character_id);
            }
            break;
        }
      }
    })();
  }

  applyHealthLevelDamage(targetType: string, targetId: number, damage: DamageObject) {
    // Robust, HealthTracker-based health/damage logic for target (character or npc)
    const table = targetType === 'character' ? 'characters' : 'npcs';
    let rec: any = this.db.prepare(`SELECT id, health_levels FROM ${table} WHERE id = ?`).get(targetId);
    if (!rec) return { success: false, message: `${targetType} not found` };

    const tracker = HealthTracker.from(rec.health_levels);
    const changed = tracker.applyDamage(damage);

    // Save back if changed
    if (changed) {
      this.db.prepare(`UPDATE ${table} SET health_levels = ? WHERE id = ?`).run(tracker.serialize(), targetId);
    }

    const boxes = tracker.getBoxArray();
    const penalty = tracker.getWoundPenalty();
    const statusText = `Health: ${boxes.join('|')} | Penalty: ${penalty}`;

    return { success: true, newHealth: boxes, woundPenalty: penalty, statusText };
  }

  // Add an entry to the experience_ledger for XP tracking
  addExperienceLedgerEntry(character_id: number, amount: number, reason: string) {
    this.db.prepare(
      `INSERT INTO experience_ledger (character_id, amount, reason) VALUES (?, ?, ?)`
    ).run(character_id, amount, reason);
  }

  listCharacters(): any[] {
    return this.db.prepare('SELECT id, name, game_line, concept FROM characters ORDER BY name').all();
  }

  listAntagonists(): any[] {
    return this.db.prepare('SELECT id, name, template, game_line, concept FROM npcs ORDER BY name').all();
  }

  // --- Inventory Management ---
  addItem(character_id: number, item: { name: string; type?: string; quantity?: number; description?: string; properties?: any; }) {
    const stmt = this.db.prepare(`
      INSERT INTO inventory (character_id, item_name, item_type, quantity, description, properties)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(
      character_id,
      item.name,
      item.type || 'misc',
      item.quantity || 1,
      item.description || null,
      item.properties ? JSON.stringify(item.properties) : null
    );
    return { id: result.lastInsertRowid, ...item };
  }

  getInventory(character_id: number) {
    const stmt = this.db.prepare('SELECT * FROM inventory WHERE character_id = ?');
    return stmt.all(character_id).map((item: any) => ({
      ...item,
      properties: item.properties ? JSON.parse(item.properties) : null,
    }));
  }

  updateItem(item_id: number, updates: { quantity?: number; description?: string; properties?: any }) {
    const fields = Object.keys(updates);
    const values = fields.map(key => {
      const value = (updates as any)[key];
      return typeof value === 'object' ? JSON.stringify(value) : value;
    });

    if (fields.length === 0) return;
    const setClause = fields.map(f => `${f} = ?`).join(', ');
    this.db.prepare(`UPDATE inventory SET ${setClause} WHERE id = ?`).run(...values, item_id);
  }

  removeItem(item_id: number) {
    const res = this.db.prepare('DELETE FROM inventory WHERE id = ?').run(item_id);
    return res.changes > 0;
  }

}
