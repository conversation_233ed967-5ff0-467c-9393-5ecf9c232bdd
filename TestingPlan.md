# **Revised & Complete MCP Testing Plan**

This document outlines a rigorous, comprehensive testing plan for every Model Context Protocol (MCP) tool defined in the World of Darkness server suite. The plan covers all available server APIs (`game-state-server` and `combat-engine-server`), providing thorough test cases, clear methodologies, expected results, and rationales to ensure reliability, correctness, and robustness.

## Table of Contents
*   **Game-State Server Tools**
    *   Character & Antagonist Management: `create_character`, `get_character`, `update_character`, `list_characters`, `create_antagonist`, `get_antagonist`, `update_antagonist`, `list_antagonists`
    *   Resource & Health: `spend_resource`, `restore_resource`, `gain_resource`, `apply_damage`
    *   Progression (XP): `award_xp`, `spend_xp`, `improve_trait`, `get_trait_improvement_cost`
    *   Status Effects: `apply_status_effect`, `remove_status_effect`, `get_status_effects`
    *   Inventory: `add_item`, `get_inventory`, `update_item`, `remove_item`
    *   Persistence: `save_world_state`, `get_world_state`, `save_story_progress`
*   **Combat-Engine Server Tools**
    *   Core Dice Mechanics: `roll_wod_pool`, `roll_contested_action`, `roll_soak`, `roll_damage_pool`
    *   Initiative & Turn Management: `roll_initiative_for_scene`, `set_initiative`, `get_initiative_order`, `advance_turn`, `get_current_turn`
    *   Game-Line Specific Mechanics: `roll_virtue_check`, `change_form`, `spend_rage_for_extra_actions`, `roll_magick_effect`, `invoke_cantrip`
    *   Social Combat: `roll_social_combat`

---

## **`game-state-server` Tools**

### Character & Antagonist Management

#### `create_character` & `create_antagonist`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Standard Creation** | Verify successful creation for each splat. | `{ "name": "Armand", "game_line": "vampire", "clan": "Toreador" }`, `{ "template_name": "Sabbat Shovelhead", "custom_name": "Rocco" }` | Character/NPC created and retrievable. Splat-specific tables populated. |
| **Edge: Minimal Input** | Ensure optional fields can be omitted. | `{ "name": "Elsa", "game_line": "werewolf" }` | Character created with default values for all omitted fields. |
| **Validation: Missing Required** | Fail if required fields like `name` or `game_line` are missing. | `{ "game_line": "mage" }` | Error message: "Missing required field: name". |
| **Validation: Invalid Enum** | Reject invalid `game_line` or splat-specific enums. | `{ "name": "Test", "game_line": "dragon" }` | Error message: "Invalid value for game_line". |
| **Negative: Duplicate Name** | Reject duplicate character names. | Create "Armand" twice. | `UNIQUE constraint failed` error on second attempt. |
| **Integration: Usability** | Ensure newly created entity can be used in other tools. | Create char, then `apply_damage` using its new ID. | Both tool calls succeed. |

</details>

---
#### `get_character` & `get_character_by_name` / `get_antagonist`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Standard Get by ID/Name** | Retrieve existing entities successfully. | `{ "character_id": 1 }`, `{ "name": "Armand" }` | Full, correctly formatted character sheet is returned. |
| **Splat-Specific Data** | Verify all splat-specific data is joined and returned. | Get a Werewolf character. | Response includes Rage, Gnosis, Gifts, etc. |
| **Negative: Nonexistent** | Handle queries for nonexistent entities gracefully. | `{ "character_id": 99999 }` | Clear "Not Found" error message. |
| **Validation: Invalid Type** | Reject non-integer IDs or non-string names. | `{ "character_id": "abc" }` | Input validation error. |

</details>

---
#### `update_character` & `update_antagonist`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Standard Update** | Change a single, simple trait. | `{ "character_id": 1, "updates": { "concept": "Survivor" } }` | Success confirmation. `get_character` reflects the change. |
| **Splat-Specific Update** | Update a trait in a joined table (e.g., `humanity`). | `{ "character_id": 1, "updates": { "humanity": 6 } }` | Success confirmation. `get_character` shows new humanity. |
| **Validation: Invalid Field** | Reject updates to fields that do not exist. | `{ "character_id": 1, "updates": { "luck_points": 5 } }` | Error message: "Invalid field 'luck_points'". |
| **Validation: Data Type Mismatch** | Reject updates with incorrect data types. | `{ "character_id": 1, "updates": { "strength": "strong" } }` | Input validation error. |

</details>

---
### Resource, Health, & Progression

#### `spend_resource` & `restore_resource`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Standard Spend/Restore** | Spend/restore a valid resource. | `{ "character_id": 1, "resource_name": "willpower", "amount": 1 }` | Success message with new and max values (e.g., "Willpower: 4/5"). |
| **Validation: Insufficient** | Prevent spending more than available. | Spend 10 Willpower when character has 5. | Error: "Not enough willpower. Has 5, needs 10." |
| **Validation: Over-Restoring** | Prevent restoring beyond the permanent maximum. | Restore 3 Willpower when at 4/5. | Success message. New value is 5/5 (capped at max). |
| **Validation: Invalid Resource** | Reject spending a resource the character doesn't have. | `spend_resource` with `resource_name: "blood"` on a Mage character. | Error: "Invalid resource 'blood' for game_line 'mage'". |

</details>

---
#### `gain_resource`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Standard Gain** | Gain a resource from an action. | `{ "character_id": 1, "resource_name": "blood", "roll_successes": 3 }` | Success message. Blood pool increases by 3 (up to max). |
| **Validation: Invalid Resource** | Reject gaining a resource not applicable to the game line. | Gain 'gnosis' for a Vampire. | Error message. |
| **Validation: Non-Positive** | Reject zero or negative successes. | `{ ..., "roll_successes": 0 }` | Error: "roll_successes must be a positive number." |

</details>

---
#### `apply_damage`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Damage Types** | Verify Bashing, Lethal, and Aggravated damage apply correctly. | Apply 2 Bashing, then 1 Lethal. | Bashing upgrades to Lethal. Health track shows `X|X|X| | | |`. |
| **Incapacitated/Overflow** | Test damage that fills or exceeds the health track. | Apply 8 Lethal damage. | Health track is full of 'X'. Status is Incapacitated. |
| **Integration** | Ensure wound penalties are reflected in subsequent rolls. | Apply 3 Lethal damage, then `roll_wod_pool`. | A -1 wound penalty should be noted/applied to the roll. |

</details>

---
#### `award_xp`, `spend_xp`, `improve_trait`, `get_trait_improvement_cost`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **XP Flow** | Award, check cost, improve, and verify new XP total. | `award_xp`, `get_trait_improvement_cost`, `improve_trait`. | Each step succeeds. `get_character` shows increased trait and decreased XP. |
| **Cost Calculation** | Verify cost calculation is correct for all trait types. | `get_trait_improvement_cost` for Attribute, Ability, Discipline, etc. | Correct costs returned (e.g., Attribute = new rating * 4). |
| **Validation: Insufficient XP** | Prevent improving a trait without enough XP. | `improve_trait` when XP is too low. | Error: "Not enough XP." |
| **Validation: Invalid Trait** | Reject attempts to improve a nonexistent trait. | `improve_trait` with `trait_name: "Cooking"`. | Error: "Trait 'Cooking' not found." |

</details>

---

## **`combat-engine-server` Tools**

### Core Dice Mechanics

#### `roll_wod_pool` & `roll_contested_action`
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Standard Roll** | Verify basic success/failure/botch logic. | `{ "pool_size": 5, "difficulty": 6 }` | Correct number of successes calculated. |
| **Specialty Rule** | Ensure a '10' counts as two successes when specialty is true. | `{ "pool_size": 3, "difficulty": 6, "has_specialty": true }` | Rolls of 10 add 2 successes. |
| **Zero/Negative Pool** | Handle zero or negative dice pools gracefully. | `{ "pool_size": 0 }` | Rolls 1 chance die (10=success, 1=botch). `{ "pool_size": -1 }` -> Error. |
| **Contested Logic** | Verify net successes and tie/botch resolution. | Attacker gets 3 successes, Defender gets 1. | "Attacker wins by 2 net successes." |

</details>

---
### Game-Line Specific Mechanics

#### `roll_virtue_check` (Vampire)
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Frenzy/Rötschreck** | Simulate resisting a fear or anger frenzy. | `{ "character_id": 1, "virtue_name": "self-control", "difficulty": 8 }` | Success/failure based on Self-Control roll. |

</details>

---
#### `change_form` & `spend_rage_for_extra_actions` (Werewolf)
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Form Modifiers** | Verify correct attribute modifiers are returned for each form. | `{ "character_id": 2, "target_form": "Crinos" }` | Returns `{ "str": +4, "dex": +1, "sta": +3, ... }`. |
| **Rage for Actions** | Confirm the tool returns a valid confirmation. | `{ "character_id": 2, "actions_to_gain": 2 }` | Success message. Game-state should reflect Rage spent. |

</details>

---
#### `roll_magick_effect` (Mage)
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Coincidental vs. Vulgar** | Test both coincidental and vulgar magick. | `{ ..., "is_coincidental": true }` vs. `{ ..., "is_coincidental": false }` | Vulgar effect that fails generates Paradox points. |
| **Paradox Backlash** | A roll that botches should trigger a significant Paradox effect. | Botch a vulgar roll. | Tool returns a high number of Paradox points and a narrative of a backlash. |

</details>

---
#### `invoke_cantrip` (Changeling)
<details>
<summary>Expand Test Cases</summary>

| Test Case | Goal | Test Input | Expected Output |
| :--- | :--- | :--- | :--- |
| **Art + Realm Pool** | Verify the dice pool is calculated correctly from Art + Realm. | `{ "art_pool": 3, "realm_pool": 2, ... }` | Tool rolls a pool of 5 dice. |
| **Banality Trigger** | A botch should trigger a Banality check or consequence. | Botch a cantrip roll. | Tool returns a botch result and a narrative suggestion about Banality. |

</details>