"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleToolRequest = handleToolRequest;
// Utility: Serialize any array of strings/objects as { type: 'text', text: string }[] for MCP compliance
function makeTextContentArray(contentArr) {
    return contentArr.map(function (entry) {
        if (typeof entry === "string") {
            return { type: 'text', text: entry };
        }
        if (entry && typeof entry === "object" && entry.type === "text" && typeof entry.text === "string") {
            // Already compliant
            return entry;
        }
        // For any other objects/values, serialize as prettified JSON
        return { type: 'text', text: JSON.stringify(entry, null, 2) };
    });
}
// File: game-state-server/src/index.ts
var index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
var stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
var types_js_1 = require("@modelcontextprotocol/sdk/types.js");
var db_js_1 = require("./db.js");
var characterSheets_js_1 = require("./characterSheets.js");
var db = new db_js_1.GameDatabase();
var server = new index_js_1.Server({ name: 'rpg-game-state-server', version: '2.1.0' }, { capabilities: { tools: {} } });
var toolDefinitions = [
    {
        name: 'create_character',
        description: 'Create a new oWoD character.',
        inputSchema: {
            type: 'object',
            properties: {
                // Core character properties
                name: { type: 'string', description: 'Character name' },
                concept: { type: 'string', description: 'Character concept', nullable: true },
                game_line: { type: 'string', enum: ['vampire', 'werewolf', 'mage', 'changeling'], description: 'Game line/splat' },
                // Attributes and basic traits are filled in backend with defaults or can be optionally included
                // --- Vampire-specific fields
                clan: { type: 'string', description: 'Vampire clan (e.g., Brujah, Malkavian)', nullable: true },
                generation: { type: 'number', description: 'Vampire generation', nullable: true },
                blood_pool_current: { type: 'number', description: 'Current Blood Pool', nullable: true },
                blood_pool_max: { type: 'number', description: 'Max Blood Pool', nullable: true },
                humanity: { type: 'number', description: 'Humanity (Vampire only)', nullable: true },
                // --- Werewolf-specific fields
                breed: { type: 'string', description: 'Werewolf breed (e.g., Homid, Metis, Lupus)', nullable: true },
                auspice: { type: 'string', description: 'Werewolf auspice (e.g., Ragabash, Theurge)', nullable: true },
                tribe: { type: 'string', description: 'Werewolf tribe', nullable: true },
                gnosis_current: { type: 'number', description: 'Current Gnosis', nullable: true },
                gnosis_permanent: { type: 'number', description: 'Permanent Gnosis', nullable: true },
                rage_current: { type: 'number', description: 'Current Rage', nullable: true },
                rage_permanent: { type: 'number', description: 'Permanent Rage', nullable: true },
                renown_glory: { type: 'number', description: 'Glory Renown', nullable: true },
                renown_honor: { type: 'number', description: 'Honor Renown', nullable: true },
                renown_wisdom: { type: 'number', description: 'Wisdom Renown', nullable: true },
                // --- Mage-specific fields
                tradition_convention: { type: 'string', description: 'Mage tradition or Convention', nullable: true },
                arete: { type: 'number', description: 'Mage Arete', nullable: true },
                quintessence: { type: 'number', description: 'Mage Quintessence', nullable: true },
                paradox: { type: 'number', description: 'Mage Paradox', nullable: true },
                // --- Changeling-specific fields
                kith: { type: 'string', description: 'Changeling kith', nullable: true },
                seeming: { type: 'string', description: 'Changeling seeming', nullable: true },
                glamour_current: { type: 'number', description: 'Current Glamour', nullable: true },
                glamour_permanent: { type: 'number', description: 'Permanent Glamour', nullable: true },
                banality_permanent: { type: 'number', description: 'Permanent Banality', nullable: true },
                // Optionally add abilities, disciplines, spheres, arts, realms, etc.
                abilities: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Starting abilities for the character' },
                disciplines: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Starting disciplines (Vampire only)' },
                spheres: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Spheres (Mage only)' },
                arts: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Changeling Arts' },
                realms: { type: 'array', items: { type: 'object' }, nullable: true, description: 'Changeling Realms' }
            },
            required: ['name', 'game_line']
        }
    },
    {
        name: 'get_character',
        description: 'Retrieve full character data.',
        inputSchema: {
            type: 'object',
            properties: { character_id: { type: 'number' } },
            required: ['character_id']
        }
    },
    {
        name: 'get_character_by_name',
        description: 'Retrieve character by name.',
        inputSchema: {
            type: 'object',
            properties: { name: { type: 'string' } },
            required: ['name']
        }
    },
    {
        name: 'update_character',
        description: 'Update character traits.',
        inputSchema: {
            type: 'object',
            properties: {
                character_id: { type: 'number' },
                updates: { type: 'object' }
            },
            required: ['character_id', 'updates']
        }
    },
    {
        name: 'spend_resource',
        description: 'Spend a character resource.',
        inputSchema: {
            type: 'object',
            properties: {
                character_id: { type: 'number' },
                resource_name: { type: 'string', enum: ['willpower', 'blood', 'gnosis', 'rage', 'glamour', 'quintessence', 'paradox'] },
                amount: { type: 'number', default: 1 }
            },
            required: ['character_id', 'resource_name']
        }
    },
    {
        name: "restore_resource",
        description: "Restore a character resource like Willpower, Blood, etc.",
        inputSchema: {
            type: "object",
            properties: {
                character_id: { type: "number" },
                resource_name: { type: "string", enum: ['willpower', 'blood', 'gnosis', 'rage', 'glamour', 'quintessence'] },
                amount: { type: 'number', default: 1 }
            },
            required: ['character_id', 'resource_name']
        }
    },
    {
        name: 'apply_damage',
        description: 'Apply health level damage to a target after a successful damage roll.',
        inputSchema: {
            type: 'object',
            properties: {
                target_type: { type: 'string', enum: ['character', 'npc'] },
                target_id: { type: 'number' },
                damage_successes: { type: 'number', description: 'The number of successes from the damage roll.' },
                damage_type: { type: 'string', enum: ['bashing', 'lethal', 'aggravated'], default: 'lethal' }
            },
            required: ['target_type', 'target_id', 'damage_successes', 'damage_type']
        }
    },
    {
        name: 'create_antagonist',
        description: 'Create an antagonist from a template.',
        inputSchema: {
            type: 'object',
            properties: {
                template_name: { type: 'string' },
                custom_name: { type: 'string' }
            },
            required: ['template_name']
        }
    },
    {
        name: 'get_antagonist',
        description: 'Retrieve antagonist data by ID.',
        inputSchema: {
            type: 'object',
            properties: { npc_id: { type: 'number' } },
            required: ['npc_id']
        }
    },
    ,
    // --- Gain Resource Tool Definition ---
    {
        name: 'gain_resource',
        description: 'Gain a resource through an in-game action (e.g., feeding, meditation, quest). Applies game-line–specific logic.',
        inputSchema: {
            type: 'object',
            properties: {
                character_id: { type: 'number' },
                resource_name: { type: 'string', enum: ['willpower', 'blood', 'gnosis', 'glamour', 'quintessence'] },
                roll_successes: { type: 'number', minimum: 1 }
            },
            required: ['character_id', 'resource_name', 'roll_successes']
        }
    }
];
// --- Inventory, World/Story, Antagonist, and Roster Management Tool Definitions ---
toolDefinitions.push(
// Inventory Management
{
    name: 'add_item',
    description: 'Add an item to a character\'s inventory.',
    inputSchema: {
        type: 'object',
        properties: {
            character_id: { type: 'number' },
            item: { type: 'object' }
        },
        required: ['character_id', 'item']
    }
}, {
    name: 'get_inventory',
    description: 'Get a list of items in a character\'s inventory.',
    inputSchema: {
        type: 'object',
        properties: {
            character_id: { type: 'number' }
        },
        required: ['character_id']
    }
}, {
    name: 'update_item',
    description: 'Update an item\'s quantity or other properties.',
    inputSchema: {
        type: 'object',
        properties: {
            item_id: { type: 'number' },
            updates: { type: 'object' }
        },
        required: ['item_id', 'updates']
    }
}, {
    name: 'remove_item',
    description: 'Remove an item from inventory by its ID.',
    inputSchema: {
        type: 'object',
        properties: {
            item_id: { type: 'number' }
        },
        required: ['item_id']
    }
}, 
// World/Story Persistence
{
    name: 'save_world_state',
    description: 'Saves the current state of the game world.',
    inputSchema: {
        type: 'object',
        properties: {
            location: { type: 'string' },
            notes: { type: 'string' },
            data: { type: 'object' }
        },
        required: ['location', 'notes', 'data']
    }
}, {
    name: 'get_world_state',
    description: 'Retrieves the last saved state of the game world.',
    inputSchema: {
        type: 'object',
        properties: {}
    }
}, {
    name: 'save_story_progress',
    description: 'Logs a narrative checkpoint in the story.',
    inputSchema: {
        type: 'object',
        properties: {
            chapter: { type: ['string', 'number'] },
            scene: { type: ['string', 'number'] },
            summary: { type: 'string' }
        },
        required: ['chapter', 'scene', 'summary']
    }
}, 
// Antagonist and Roster Management
{
    name: 'update_antagonist',
    description: 'Update an antagonist\'s stats or details.',
    inputSchema: {
        type: 'object',
        properties: {
            npc_id: { type: 'number' },
            updates: { type: 'object' }
        },
        required: ['npc_id', 'updates']
    }
}, {
    name: 'list_antagonists',
    description: 'Lists all created antagonists.',
    inputSchema: {
        type: 'object',
        properties: {}
    }
}, {
    name: 'remove_antagonist',
    description: 'Permanently removes an antagonist from the game.',
    inputSchema: {
        type: 'object',
        properties: {
            npc_id: { type: 'number' }
        },
        required: ['npc_id']
    }
}, {
    name: 'list_characters',
    description: 'Lists all created player characters.',
    inputSchema: {
        type: 'object',
        properties: {}
    }
});
// --- Initiative tool definitions ---
toolDefinitions.push({
    name: "set_initiative",
    description: "Set the initiative order for a scene. Overwrites all entries for that scene. Each entry may be a PC or NPC.",
    inputSchema: {
        type: "object",
        properties: {
            scene_id: { type: "string" },
            entries: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        character_id: { type: ["number", "null"] },
                        npc_id: { type: ["number", "null"] },
                        actor_name: { type: "string" },
                        initiative_score: { type: "number" },
                        turn_order: { type: "number" }
                    },
                    required: ["actor_name", "initiative_score", "turn_order"]
                }
            }
        },
        required: ["scene_id", "entries"]
    }
}, {
    name: "get_initiative_order",
    description: "Get current initiative order for the specified scene.",
    inputSchema: {
        type: "object",
        properties: {
            scene_id: { type: "string" }
        },
        required: ["scene_id"]
    }
});
// Combat Turn Management:
toolDefinitions.push({
    name: 'advance_turn',
    description: 'Advances to the next actor in the initiative order for a scene.',
    inputSchema: {
        type: 'object',
        properties: { scene_id: { type: 'string' } },
        required: ['scene_id']
    }
}, {
    name: 'get_current_turn',
    description: 'Retrieve the current actor and round info for a combat scene.',
    inputSchema: {
        type: 'object',
        properties: { scene_id: { type: 'string' } },
        required: ['scene_id']
    }
});
// XP management tools:
toolDefinitions.push({
    name: 'award_xp',
    description: 'Award experience points to a character.',
    inputSchema: {
        type: 'object',
        properties: {
            character_id: { type: 'number' },
            amount: { type: 'number', minimum: 1 },
            reason: { type: 'string' }
        },
        required: ['character_id', 'amount', 'reason']
    }
}, {
    name: 'spend_xp',
    description: 'Spend a character\'s XP to improve a trait (logging only; does not yet update trait).',
    inputSchema: {
        type: 'object',
        properties: {
            character_id: { type: 'number' },
            amount: { type: 'number', minimum: 1 },
            reason: { type: 'string' },
            trait_name: { type: 'string' },
            trait_info: { type: 'object' }
        },
        required: ['character_id', 'amount', 'reason']
    }
});
// -- Add improve_trait tool schema
toolDefinitions.push({
    name: 'improve_trait',
    description: 'Increase a trait for a character by spending XP according to oWoD rules. Computes XP cost, checks XP, applies change and deduction. Supported trait_types: attribute, ability, discipline, sphere, art, realm, willpower, power_stat. trait_name must match the trait/facet name, e.g. "strength" or "Firearms".',
    inputSchema: {
        type: 'object',
        properties: {
            character_id: { type: 'number' },
            trait_type: {
                type: 'string',
                enum: ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat']
            },
            trait_name: { type: 'string' }
        },
        required: ['character_id', 'trait_type', 'trait_name']
    }
});
/**
 * Calculates the XP cost to improve a character trait to the next level.
 */
toolDefinitions.push({
    name: 'get_trait_improvement_cost',
    description: 'Calculates the XP cost to improve a character trait to the next level.',
    inputSchema: {
        type: 'object',
        properties: {
            character_id: { type: 'number' },
            trait_type: {
                type: 'string',
                enum: ['attribute', 'ability', 'discipline', 'sphere', 'art', 'realm', 'willpower', 'power_stat']
            },
            trait_name: { type: 'string' }
        },
        required: ['character_id', 'trait_type', 'trait_name']
    }
});
server.setRequestHandler(types_js_1.ListToolsRequestSchema, function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, ({
                tools: toolDefinitions
            })];
    });
}); });
function handleToolRequest(request) {
    return __awaiter(this, void 0, void 0, function () {
        // Centralized by game_line for future extensibility
        function getResourceMapping(game_line) {
            switch (game_line) {
                case 'vampire':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        blood: { column: 'blood_pool_current', maxColumn: 'blood_pool_max' },
                        humanity: { column: 'humanity' }
                    };
                case 'werewolf':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        gnosis: { column: 'gnosis_current', maxColumn: 'gnosis_permanent' },
                        rage: { column: 'rage_current', maxColumn: 'rage_permanent' }
                    };
                case 'mage':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        quintessence: { column: 'quintessence' },
                        paradox: { column: 'paradox' }
                    };
                case 'changeling':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        glamour: { column: 'glamour_current', maxColumn: 'glamour_permanent' }
                    };
                default:
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' }
                    };
            }
        }
        function getResourceMapping(game_line) {
            switch (game_line) {
                case 'vampire':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        blood: { column: 'blood_pool_current', maxColumn: 'blood_pool_max' },
                        humanity: { column: 'humanity' }
                    };
                case 'werewolf':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        gnosis: { column: 'gnosis_current', maxColumn: 'gnosis_permanent' },
                        rage: { column: 'rage_current', maxColumn: 'rage_permanent' }
                    };
                case 'mage':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        quintessence: { column: 'quintessence' },
                        paradox: { column: 'paradox' }
                    };
                case 'changeling':
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' },
                        glamour: { column: 'glamour_current', maxColumn: 'glamour_permanent' }
                    };
                default:
                    return {
                        willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent' }
                    };
            }
        }
        function getResourceMapping(game_line) {
            switch (game_line) {
                case 'vampire': return {
                    willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent', label: 'Willpower', desc: 'rest, inspiration, fulfillment' },
                    blood: { column: 'blood_pool_current', maxColumn: 'blood_pool_max', label: 'Blood Pool', desc: 'feeding' },
                    humanity: { column: 'humanity', label: 'Humanity', desc: 'enlightenment' }
                };
                case 'werewolf': return {
                    willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent', label: 'Willpower', desc: 'spiritual growth' },
                    gnosis: { column: 'gnosis_current', maxColumn: 'gnosis_permanent', label: 'Gnosis', desc: 'rites, quest, meditation' },
                    rage: { column: 'rage_current', maxColumn: 'rage_permanent', label: 'Rage', desc: 'stress, anger, moon' }
                };
                case 'mage': return {
                    willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent', label: 'Willpower', desc: 'nine spheres mastery' },
                    quintessence: { column: 'quintessence', label: 'Quintessence', desc: 'node or other source' },
                    paradox: { column: 'paradox', label: 'Paradox', desc: 'magick backlash' }
                };
                case 'changeling': return {
                    willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent', label: 'Willpower', desc: 'regained via rest, resolve' },
                    glamour: { column: 'glamour_current', maxColumn: 'glamour_permanent', label: 'Glamour', desc: 'enchanted action or Dreaming' }
                };
                default: return {
                    willpower: { column: 'willpower_current', maxColumn: 'willpower_permanent', label: 'Willpower', desc: 'personal force' }
                };
            }
        }
        var _a, name, args, character_id, item, newItem, inventory, output, success, location_1, notes, data, state, chapter, scene, summary, npcs, output, success, characters, output, target_type, target_id, effect_name, _b, description, _c, mechanical_effect, _d, duration_type, _e, duration_value, effectId, effect_id, ok, target_type, target_id, effects, character, sheet, character, derangements, sheet, character, derangements, sheet, character_id, updates, character_id, resource_name, _f, amount, char, resourceEntry, col, currentValue, updateSuccess, updatedChar, newTotal, narrative, character_id, resource_name, _g, amount, char, cols, currentValue, maxValue, newValue, character_id, resource_name, roll_successes, char, resConf, curr, max, gained, resultTotal, flavor, statLine, target_type, target_id, damage_successes, damage_type, damage, result, template_name, custom_name, antagonist, rec, character_id, amount, reason, char, character_id, amount, reason, trait_name, trait_info, char, character_id, trait_type, trait_name_1, char, curr_rating, new_rating, valid_trait, ab, d, sph, a, r, xp_cost, xp_formula, dbres, new_trait_val, outputText, scene_id, entries, _i, entries_1, entry, order, scene_id, order, scene_id, result, nextActor, actorName, initiative, output, scene_id, result, actor, actorName, initiative, output, character_id, trait_type, trait_name_2, char, curr_rating, ab, d, sph, a, r, new_rating, xp_cost, xp_formula;
        var _h, _j, _k;
        var _l, _m, _o;
        return __generator(this, function (_p) {
            _a = request.params, name = _a.name, args = _a.arguments;
            try {
                switch (name) {
                    // --- Inventory Management ---
                    case 'add_item': {
                        character_id = args.character_id, item = args.item;
                        newItem = db.addItem(character_id, item);
                        return [2 /*return*/, { content: [{ type: 'text', text: "\u2705 Added '".concat(newItem.name, "' to character #").concat(character_id, "'s inventory.") }] }];
                    }
                    case 'get_inventory': {
                        inventory = db.getInventory(args.character_id);
                        output = "\uD83C\uDF92 Inventory for Character #".concat(args.character_id, ":\n") +
                            (inventory.length > 0
                                ? inventory.map(function (item) { return "- ".concat(item.item_name, " (x").concat(item.quantity, ") [ID: ").concat(item.id, "]"); }).join('\n')
                                : '  (Empty)');
                        return [2 /*return*/, { content: [{ type: 'text', text: output }] }];
                    }
                    case 'update_item': {
                        db.updateItem(args.item_id, args.updates);
                        return [2 /*return*/, { content: [{ type: 'text', text: "\u2705 Item #".concat(args.item_id, " updated.") }] }];
                    }
                    case 'remove_item': {
                        success = db.removeItem(args.item_id);
                        return [2 /*return*/, { content: [{ type: 'text', text: success ? "\uD83D\uDDD1\uFE0F Item #".concat(args.item_id, " removed.") : '❌ Item not found.' }] }];
                    }
                    // --- World & Story Persistence ---
                    case 'save_world_state': {
                        location_1 = args.location, notes = args.notes, data = args.data;
                        db.saveWorldState({ location: location_1, notes: notes, data: data });
                        return [2 /*return*/, { content: [{ type: 'text', text: "\uD83C\uDF0D World state saved successfully." }] }];
                    }
                    case 'get_world_state': {
                        state = db.getWorldState();
                        return [2 /*return*/, { content: [{ type: 'text', text: state ? JSON.stringify(state, null, 2) : 'No world state saved yet.' }] }];
                    }
                    case 'save_story_progress': {
                        chapter = args.chapter, scene = args.scene, summary = args.summary;
                        db.saveStoryProgress({ chapter: chapter, scene: scene, summary: summary });
                        return [2 /*return*/, { content: [{ type: 'text', text: "\uD83D\uDCD6 Story progress logged for Chapter ".concat(chapter, ".") }] }];
                    }
                    // --- Antagonist & Character Management ---
                    case 'update_antagonist': {
                        db.updateAntagonist(args.npc_id, args.updates);
                        return [2 /*return*/, { content: [{ type: 'text', text: "\u2705 Antagonist #".concat(args.npc_id, " updated.") }] }];
                    }
                    case 'list_antagonists': {
                        npcs = db.listAntagonists();
                        output = "\uD83D\uDC65 Antagonist Roster:\n" +
                            (npcs.length > 0 ? npcs.map(function (npc) { return "- ".concat(npc.name, " (").concat(npc.game_line, ") [ID: ").concat(npc.id, "]"); }).join('\n') : '  (None)');
                        return [2 /*return*/, { content: [{ type: 'text', text: output }] }];
                    }
                    case 'remove_antagonist': {
                        success = db.removeAntagonist(args.npc_id);
                        return [2 /*return*/, { content: [{ type: 'text', text: success ? "\uD83D\uDDD1\uFE0F Antagonist #".concat(args.npc_id, " removed.") : '❌ Antagonist not found.' }] }];
                    }
                    case 'list_characters': {
                        characters = db.listCharacters();
                        output = "\uD83C\uDFAD Character Roster:\n" +
                            (characters.length > 0 ? characters.map(function (char) { return "- ".concat(char.name, " (").concat(char.game_line, ") [ID: ").concat(char.id, "]"); }).join('\n') : '  (None)');
                        return [2 /*return*/, { content: [{ type: 'text', text: output }] }];
                    }
                    // ---- STATUS EFFECTS SYSTEM ----
                    case 'apply_status_effect': {
                        target_type = args.target_type, target_id = args.target_id, effect_name = args.effect_name, _b = args.description, description = _b === void 0 ? '' : _b, _c = args.mechanical_effect, mechanical_effect = _c === void 0 ? {} : _c, _d = args.duration_type, duration_type = _d === void 0 ? 'indefinite' : _d, _e = args.duration_value, duration_value = _e === void 0 ? null : _e;
                        effectId = db.addStatusEffect({
                            target_type: target_type,
                            target_id: target_id,
                            effect_name: effect_name,
                            description: description,
                            mechanical_effect: mechanical_effect,
                            duration_type: duration_type,
                            duration_value: duration_value,
                        });
                        return [2 /*return*/, {
                                content: [
                                    { type: 'text', text: "\uD83C\uDF00 Status effect '".concat(effect_name, "' applied to ").concat(target_type, " #").concat(target_id, " (ID: ").concat(effectId, ")") },
                                    { type: 'text', text: JSON.stringify({ effect_id: effectId, target_type: target_type, target_id: target_id, effect_name: effect_name, duration_type: duration_type, duration_value: duration_value }) }
                                ]
                            }];
                    }
                    case 'remove_status_effect': {
                        effect_id = args.effect_id;
                        ok = db.removeStatusEffect(effect_id);
                        if (!ok) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Status effect ID ".concat(effect_id, " not found.") }], isError: true }];
                        }
                        return [2 /*return*/, { content: [{ type: 'text', text: "\u2705 Status effect ".concat(effect_id, " removed.") }] }];
                    }
                    case 'get_status_effects': {
                        target_type = args.target_type, target_id = args.target_id;
                        effects = db.listStatusEffects(target_type, target_id);
                        return [2 /*return*/, {
                                content: [
                                    { type: 'text', text: JSON.stringify({ target_type: target_type, target_id: target_id, effects: effects }) }
                                ]
                            }];
                    }
                    case 'create_character': {
                        character = db.createCharacter(args);
                        if (!character)
                            throw new Error("Character creation failed in database.");
                        sheet = (0, characterSheets_js_1.formatSheetByGameLine)({ character: character });
                        return [2 /*return*/, { content: [{ type: 'text', text: typeof sheet === 'string' ? sheet : JSON.stringify(sheet) }] }];
                    }
                    case 'get_character': {
                        character = db.getCharacter(args.character_id);
                        if (!character)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        derangements = db.getDerangements(args.character_id);
                        sheet = (0, characterSheets_js_1.formatSheetByGameLine)({ character: character, derangements: derangements });
                        return [2 /*return*/, { content: [{ type: 'text', text: typeof sheet === 'string' ? sheet : JSON.stringify(sheet) }] }];
                    }
                    case 'get_character_by_name': {
                        character = db.getCharacterByName(args.name);
                        if (!character)
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C No character found with name \"".concat(args.name, "\"") }] }];
                        derangements = db.getDerangements(character.id);
                        sheet = (0, characterSheets_js_1.formatSheetByGameLine)({ character: character, derangements: derangements });
                        return [2 /*return*/, { content: [{ type: 'text', text: typeof sheet === 'string' ? sheet : JSON.stringify(sheet) }] }];
                    }
                    case 'update_character': {
                        character_id = args.character_id, updates = args.updates;
                        db.updateCharacter(character_id, updates);
                        return [2 /*return*/, { content: [{ type: 'text', text: "\u2705 Character #".concat(character_id, " updated.") }] }];
                    }
                    case 'spend_resource': {
                        character_id = args.character_id, resource_name = args.resource_name, _f = args.amount, amount = _f === void 0 ? 1 : _f;
                        char = db.getCharacter(character_id);
                        if (!char)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        resourceEntry = getResourceMapping(char.game_line)[resource_name];
                        if (!resourceEntry) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Unknown resource '".concat(resource_name, "' for ").concat(char.game_line) }], isError: true }];
                        }
                        col = resourceEntry.column;
                        currentValue = char[col] || 0;
                        if (currentValue < amount) {
                            return [2 /*return*/, {
                                    content: [{ type: 'text', text: "\u274C Cannot spend ".concat(amount, " ").concat(resource_name, ". Only ").concat(currentValue, " available.") }],
                                    isError: true
                                }];
                        }
                        updateSuccess = false;
                        try {
                            db.updateCharacter(character_id, (_h = {}, _h[col] = currentValue - amount, _h));
                            updateSuccess = true;
                        }
                        catch (e) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Failed to update resource: ".concat(e.message) }], isError: true }];
                        }
                        updatedChar = db.getCharacter(character_id);
                        if (!updatedChar) {
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found after update!' }], isError: true }];
                        }
                        newTotal = updatedChar[col] || 0;
                        narrative = "".concat(char.name, " spent ").concat(amount, " ").concat(resource_name, ". Remaining: ").concat(newTotal);
                        return [2 /*return*/, {
                                content: makeTextContentArray([
                                    narrative,
                                    {
                                        tool_outputs: {
                                            success: true,
                                            resource_spent: resource_name,
                                            amount_spent: amount,
                                            remaining: newTotal
                                        }
                                    }
                                ])
                            }];
                    }
                    case 'restore_resource': {
                        character_id = args.character_id, resource_name = args.resource_name, _g = args.amount, amount = _g === void 0 ? 1 : _g;
                        char = db.getCharacter(character_id);
                        if (!char)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        cols = getResourceMapping(char.game_line)[resource_name];
                        if (!cols)
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Unknown resource '".concat(resource_name, "' for ").concat(char.game_line) }], isError: true }];
                        currentValue = char[cols.column] || 0;
                        maxValue = cols.maxColumn ? (char[cols.maxColumn] || currentValue) : currentValue;
                        newValue = Math.min(maxValue, currentValue + amount);
                        db.updateCharacter(character_id, (_j = {}, _j[cols.column] = newValue, _j));
                        return [2 /*return*/, {
                                content: makeTextContentArray([
                                    "\u2705 Restored ".concat(amount, " ").concat(resource_name, ". New total: ").concat(newValue, "/").concat(maxValue)
                                ])
                            }];
                    }
                    case 'gain_resource': {
                        character_id = args.character_id, resource_name = args.resource_name, roll_successes = args.roll_successes;
                        char = db.getCharacter(character_id);
                        if (!char)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        resConf = getResourceMapping(char.game_line)[resource_name];
                        if (!resConf)
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Unknown resource '".concat(resource_name, "' for ").concat(char.game_line) }], isError: true }];
                        curr = char[resConf.column] || 0;
                        max = resConf.maxColumn ? ((_l = char[resConf.maxColumn]) !== null && _l !== void 0 ? _l : curr) : undefined;
                        gained = roll_successes;
                        resultTotal = typeof max === "number" ? Math.min(max, curr + gained) : curr + gained;
                        // Update DB
                        db.updateCharacter(character_id, (_k = {}, _k[resConf.column] = resultTotal, _k));
                        flavor = '';
                        switch (resource_name) {
                            case 'blood':
                                flavor = "\uD83E\uDE78 ".concat(char.name, " fed and gained ").concat(gained, " Blood.");
                                break;
                            case 'gnosis':
                                flavor = "\uD83C\uDF0C ".concat(char.name, " gained ").concat(gained, " Gnosis (").concat(resConf.desc, ").");
                                break;
                            case 'glamour':
                                flavor = "\uD83C\uDFAD ".concat(char.name, " gained ").concat(gained, " Glamour (").concat(resConf.desc, ").");
                                break;
                            case 'quintessence':
                                flavor = "\uD83D\uDD2E ".concat(char.name, " gained ").concat(gained, " Quintessence (").concat(resConf.desc, ").");
                                break;
                            case 'willpower':
                                flavor = "\uD83D\uDCAA ".concat(char.name, " recovered ").concat(gained, " Willpower (").concat(resConf.desc, ").");
                                break;
                            case 'rage':
                                flavor = "\uD83D\uDCA2 ".concat(char.name, " regained ").concat(gained, " Rage (").concat(resConf.desc, ").");
                                break;
                            default:
                                flavor = "".concat(char.name, " gained ").concat(gained, " ").concat(resConf.label, ".");
                        }
                        statLine = "".concat(resConf.label, ": ").concat(resultTotal).concat(max ? "/".concat(max) : '');
                        return [2 /*return*/, {
                                content: makeTextContentArray([
                                    "".concat(flavor, "\n").concat(statLine),
                                    { resource: resource_name, gained: gained, new_total: resultTotal, character_id: character_id }
                                ])
                            }];
                    }
                    case 'apply_damage': {
                        target_type = args.target_type, target_id = args.target_id, damage_successes = args.damage_successes, damage_type = args.damage_type;
                        damage = { bashing: 0, lethal: 0, aggravated: 0 };
                        damage[damage_type] = damage_successes;
                        result = db.applyHealthLevelDamage(target_type, target_id, damage);
                        if (!result.success)
                            return [2 /*return*/, { content: makeTextContentArray(["\u274C ".concat(result.message)]), isError: true }];
                        return [2 /*return*/, {
                                content: makeTextContentArray([
                                    "\uD83D\uDCA5 Damage applied. ".concat(result.statusText)
                                ])
                            }];
                    }
                    case 'create_antagonist': {
                        template_name = args.template_name, custom_name = args.custom_name;
                        antagonist = db.createAntagonist(template_name, custom_name);
                        if (!antagonist)
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Template '".concat(template_name, "' not found.") }], isError: true }];
                        return [2 /*return*/, { content: [{ type: 'text', text: "Antagonist '".concat(antagonist.name, "' created.") }] }];
                    }
                    case 'get_antagonist': {
                        rec = db.getAntagonistById(args.npc_id);
                        if (!rec)
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Antagonist not found." }], isError: true }];
                        return [2 /*return*/, { content: [{ type: 'text', text: JSON.stringify(rec, null, 2) }] }];
                    }
                    case 'award_xp': {
                        character_id = args.character_id, amount = args.amount, reason = args.reason;
                        // Validation
                        if (amount <= 0)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Amount must be positive.' }], isError: true }];
                        char = db.getCharacter(character_id);
                        if (!char)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        // Insert into ledger
                        try {
                            db.addExperienceLedgerEntry(character_id, amount, reason);
                            db.updateCharacter(character_id, { experience: (char.experience || 0) + amount });
                            return [2 /*return*/, {
                                    content: [{
                                            type: 'text',
                                            text: "\u2705 Awarded ".concat(amount, " XP to '").concat(char.name, "'. Reason: ").concat(reason, "\n\nTotal XP: ").concat((char.experience || 0) + amount)
                                        }]
                                }];
                        }
                        catch (e) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Failed to award XP: ".concat(e.message) }], isError: true }];
                        }
                    }
                    case 'spend_xp': {
                        character_id = args.character_id, amount = args.amount, reason = args.reason, trait_name = args.trait_name, trait_info = args.trait_info;
                        // Validation
                        if (amount <= 0)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Amount must be positive.' }], isError: true }];
                        char = db.getCharacter(character_id);
                        if (!char)
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        if ((char.experience || 0) < amount) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Not enough XP: character only has ".concat((char.experience || 0)) }], isError: true }];
                        }
                        // Insert negative amount into ledger
                        try {
                            db.addExperienceLedgerEntry(character_id, -amount, "[SPEND] ".concat(reason));
                            db.updateCharacter(character_id, { experience: (char.experience || 0) - amount });
                            // Placeholder for trait improvement logic
                            // e.g., db.improveTrait(character_id, trait_name, trait_info)
                            return [2 /*return*/, {
                                    content: [{
                                            type: 'text',
                                            text: "\uD83D\uDFE3 ".concat(char.name, " spent ").concat(amount, " XP. Reason: ").concat(reason, "\nAffected trait: ").concat(trait_name || '[none specified]', "\nTotal XP: ").concat((char.experience || 0) - amount)
                                        }]
                                }];
                        }
                        catch (e) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Failed to spend XP: ".concat(e.message) }], isError: true }];
                        }
                    }
                    case 'improve_trait': {
                        character_id = args.character_id, trait_type = args.trait_type, trait_name_1 = args.trait_name;
                        char = db.getCharacter(character_id);
                        if (!char) {
                            return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                        }
                        curr_rating = 0, new_rating = 0;
                        valid_trait = true;
                        switch (trait_type) {
                            case 'attribute':
                                curr_rating = char[trait_name_1];
                                if (typeof curr_rating !== "number")
                                    valid_trait = false;
                                break;
                            case 'willpower':
                                curr_rating = char['willpower_permanent'];
                                if (typeof curr_rating !== "number")
                                    valid_trait = false;
                                break;
                            case 'power_stat':
                                if (char['power_stat_name'] === trait_name_1) {
                                    curr_rating = char['power_stat_rating'];
                                    if (typeof curr_rating !== 'number')
                                        valid_trait = false;
                                }
                                else {
                                    valid_trait = false;
                                }
                                break;
                            case 'ability': {
                                ab = (char.abilities || []).find(function (a) { var _a; return ((_a = a.ability_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_1.toLowerCase(); });
                                curr_rating = ab ? ab.rating : 0;
                                break;
                            }
                            case 'discipline': {
                                d = (char.disciplines || []).find(function (a) { var _a; return ((_a = a.discipline_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_1.toLowerCase(); });
                                curr_rating = d ? d.rating : 0;
                                break;
                            }
                            case 'sphere':
                                if (char.spheres) {
                                    sph = (char.spheres || []).find(function (s) { var _a; return ((_a = s.sphere_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_1.toLowerCase(); });
                                    curr_rating = sph ? sph.rating : 0;
                                }
                                break;
                            case 'art':
                                if (char.arts) {
                                    a = (char.arts || []).find(function (a) { var _a; return ((_a = a.art_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_1.toLowerCase(); });
                                    curr_rating = a ? a.rating : 0;
                                }
                                break;
                            case 'realm':
                                if (char.realms) {
                                    r = (char.realms || []).find(function (r) { var _a; return ((_a = r.realm_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_1.toLowerCase(); });
                                    curr_rating = r ? r.rating : 0;
                                }
                                break;
                            default:
                                valid_trait = false;
                        }
                        if (!valid_trait) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Unknown or untracked trait '".concat(trait_type, ":").concat(trait_name_1, "'.") }], isError: true }];
                        }
                        // 2. Calc new rating and XP cost (per oWoD core rules)
                        new_rating = curr_rating + 1;
                        xp_cost = 0, xp_formula = "";
                        switch (trait_type) {
                            case 'attribute':
                                xp_cost = new_rating * 4;
                                xp_formula = 'New rating × 4';
                                break;
                            case 'ability':
                                xp_cost = new_rating * 2;
                                xp_formula = 'New rating × 2';
                                break;
                            case 'discipline':
                                xp_cost = new_rating * 5;
                                xp_formula = 'New rating × 5';
                                break;
                            case 'sphere':
                            case 'art':
                            case 'realm':
                                xp_cost = new_rating * 7;
                                xp_formula = 'New rating × 7';
                                break;
                            case 'willpower':
                                xp_cost = 8;
                                xp_formula = 'Flat 8 XP';
                                break;
                            case 'power_stat':
                                xp_cost = new_rating * 8;
                                xp_formula = 'New rating × 8';
                                break;
                            default:
                                return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Unrecognized trait type." }], isError: true }];
                        }
                        if ((char.experience || 0) < xp_cost) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Not enough XP. ".concat(char.name, " has ").concat(char.experience, ", needs ").concat(xp_cost) }], isError: true }];
                        }
                        dbres = void 0, new_trait_val = void 0;
                        try {
                            dbres = db.improveTrait(character_id, trait_type, trait_name_1);
                            new_trait_val = (_m = dbres === null || dbres === void 0 ? void 0 : dbres.new_rating) !== null && _m !== void 0 ? _m : new_rating; // fallback: assume increment
                            xp_cost = (_o = dbres === null || dbres === void 0 ? void 0 : dbres.xp_cost) !== null && _o !== void 0 ? _o : xp_cost;
                        }
                        catch (e) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C Database update failed: ".concat(e.message) }], isError: true }];
                        }
                        outputText = "\uD83C\uDF1F TRAIT IMPROVED! \uD83C\uDF1F\n\n\uD83D\uDC64 Character: ".concat(char.name, "\n- Trait: ").concat(trait_type.toUpperCase(), " - ").concat(trait_name_1, "\n- Old Rating: ").concat(curr_rating, "\n+ New Rating: ").concat(new_trait_val || new_rating, "\n\n- XP Cost: ").concat(xp_cost, " (Rule: ").concat(xp_formula, ")\n+ Remaining XP: ").concat((char.experience || 0) - xp_cost, "\n");
                        return [2 /*return*/, {
                                content: [
                                    {
                                        type: 'text',
                                        text: outputText
                                    },
                                    {
                                        type: 'text',
                                        text: JSON.stringify({
                                            char_name: char.name,
                                            spent: xp_cost,
                                            trait_type: trait_type,
                                            trait_name: trait_name_1,
                                            previous_rating: curr_rating,
                                            new_rating: new_trait_val || new_rating,
                                            xp_formula: xp_formula,
                                            remaining_xp: (char.experience || 0) - xp_cost
                                        })
                                    }
                                ]
                            }];
                    }
                    case 'set_initiative': {
                        scene_id = args.scene_id, entries = args.entries;
                        if (!scene_id || !Array.isArray(entries)) {
                            return [2 /*return*/, {
                                    content: [{ type: 'text', text: "❌ Missing or invalid 'scene_id' or 'entries'." }],
                                    isError: true
                                }];
                        }
                        // Validate at least one entry has an actor_name & initiative_score & turn_order.
                        for (_i = 0, entries_1 = entries; _i < entries_1.length; _i++) {
                            entry = entries_1[_i];
                            if (typeof entry.actor_name !== 'string' ||
                                typeof entry.initiative_score !== 'number' ||
                                typeof entry.turn_order !== 'number') {
                                return [2 /*return*/, {
                                        content: [{ type: 'text', text: "❌ Each entry must provide 'actor_name' (string), 'initiative_score' (number), and 'turn_order' (number)." }],
                                        isError: true
                                    }];
                            }
                        }
                        db.setInitiativeOrder(scene_id, entries);
                        order = db.getInitiativeOrder(scene_id);
                        return [2 /*return*/, {
                                content: [
                                    {
                                        type: 'text',
                                        text: "\u2705 Initiative set for scene '".concat(scene_id, "'.\nOrder: ").concat(order.map(function (e) {
                                            return (e.character_id != null ? "PC#" + e.character_id : (e.npc_id != null ? "NPC#" + e.npc_id : e.actor_name)) +
                                                "(".concat(e.initiative_score, ")");
                                        }).join(", "))
                                    },
                                    { type: 'text', text: JSON.stringify({ scene_id: scene_id, initiative_order: order }) }
                                ]
                            }];
                    }
                    case 'get_initiative_order': {
                        scene_id = args.scene_id;
                        if (!scene_id) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "❌ 'scene_id' is required." }], isError: true }];
                        }
                        order = db.getInitiativeOrder(scene_id);
                        // Return direct for workflow compatibility
                        return [2 /*return*/, {
                                content: [
                                    { type: 'text', text: JSON.stringify({ scene_id: scene_id, initiative_order: order }) }
                                ]
                            }];
                    }
                    case 'advance_turn': {
                        scene_id = args.scene_id;
                        result = db.advanceTurn(scene_id);
                        if (!result.success) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C ".concat(result.message) }], isError: true }];
                        }
                        nextActor = result.next_actor;
                        actorName = (nextActor === null || nextActor === void 0 ? void 0 : nextActor.actor_name) || "Unknown";
                        initiative = (nextActor === null || nextActor === void 0 ? void 0 : nextActor.initiative_score) !== undefined ? " (Initiative: ".concat(nextActor.initiative_score, ")") : "";
                        output = "\uD83D\uDD04 Turn Advanced in Scene: ".concat(scene_id, "\n") +
                            "\u25B6\uFE0F Current Actor: ".concat(actorName).concat(initiative, "\n") +
                            "Round: ".concat(result.new_round, ", Turn: ").concat(result.new_turn_order);
                        return [2 /*return*/, { content: [{ type: 'text', text: output }] }];
                    }
                    case 'get_current_turn': {
                        scene_id = args.scene_id;
                        result = db.getCurrentTurn(scene_id);
                        if (!result.success) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C ".concat(result.message) }], isError: true }];
                        }
                        actor = result.current_actor;
                        actorName = (actor === null || actor === void 0 ? void 0 : actor.actor_name) || "Unknown";
                        initiative = (actor === null || actor === void 0 ? void 0 : actor.initiative_score) !== undefined ? " (Initiative: ".concat(actor.initiative_score, ")") : "";
                        output = "\u23F3 Current Turn in Scene: ".concat(scene_id, "\n") +
                            "\u25B6\uFE0F Actor: ".concat(actorName).concat(initiative, "\n") +
                            "Round: ".concat(result.current_round, ", Turn: ").concat(result.current_turn_order);
                        return [2 /*return*/, { content: [{ type: 'text', text: output }] }];
                    }
                    case 'get_trait_improvement_cost': {
                        character_id = args.character_id, trait_type = args.trait_type, trait_name_2 = args.trait_name;
                        // Safe-reuse the evaluation logic from db.improveTrait, but do not actually mutate anything.
                        try {
                            char = db.getCharacter(character_id);
                            if (!char) {
                                return [2 /*return*/, { content: [{ type: 'text', text: '❌ Character not found!' }], isError: true }];
                            }
                            curr_rating = 0;
                            switch (trait_type) {
                                case 'attribute':
                                    curr_rating = char[trait_name_2];
                                    if (typeof curr_rating !== "number")
                                        throw new Error("Attribute '".concat(trait_name_2, "' not found or invalid."));
                                    break;
                                case 'willpower':
                                    curr_rating = char['willpower_permanent'];
                                    if (typeof curr_rating !== "number")
                                        throw new Error("Willpower not found.");
                                    break;
                                case 'power_stat':
                                    if (char.power_stat_name === trait_name_2) {
                                        curr_rating = char.power_stat_rating;
                                        if (typeof curr_rating !== 'number')
                                            throw new Error("power_stat_rating not found.");
                                    }
                                    else {
                                        throw new Error("power_stat_name mismatch.");
                                    }
                                    break;
                                case 'ability': {
                                    ab = (char.abilities || []).find(function (a) { var _a; return ((_a = a.ability_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_2.toLowerCase(); });
                                    curr_rating = ab ? ab.rating : 0;
                                    break;
                                }
                                case 'discipline': {
                                    d = (char.disciplines || []).find(function (a) { var _a; return ((_a = a.discipline_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_2.toLowerCase(); });
                                    curr_rating = d ? d.rating : 0;
                                    break;
                                }
                                case 'sphere':
                                    if (char.spheres) {
                                        sph = (char.spheres || []).find(function (s) { var _a; return ((_a = s.sphere_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_2.toLowerCase(); });
                                        curr_rating = sph ? sph.rating : 0;
                                    }
                                    break;
                                case 'art':
                                    if (char.arts) {
                                        a = (char.arts || []).find(function (a) { var _a; return ((_a = a.art_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_2.toLowerCase(); });
                                        curr_rating = a ? a.rating : 0;
                                    }
                                    break;
                                case 'realm':
                                    if (char.realms) {
                                        r = (char.realms || []).find(function (r) { var _a; return ((_a = r.realm_name) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === trait_name_2.toLowerCase(); });
                                        curr_rating = r ? r.rating : 0;
                                    }
                                    break;
                                default:
                                    throw new Error("Unsupported trait_type: ".concat(trait_type));
                            }
                            new_rating = curr_rating + 1;
                            xp_cost = 0, xp_formula = "";
                            switch (trait_type) {
                                case 'attribute':
                                    xp_cost = new_rating * 4;
                                    xp_formula = 'New rating × 4';
                                    break;
                                case 'ability':
                                    xp_cost = new_rating * 2;
                                    xp_formula = 'New rating × 2';
                                    break;
                                case 'discipline':
                                    xp_cost = new_rating * 5;
                                    xp_formula = 'New rating × 5';
                                    break;
                                case 'sphere':
                                case 'art':
                                case 'realm':
                                    xp_cost = new_rating * 7;
                                    xp_formula = 'New rating × 7';
                                    break;
                                case 'willpower':
                                    xp_cost = 8;
                                    xp_formula = 'Flat 8 XP';
                                    break;
                                case 'power_stat':
                                    xp_cost = new_rating * 8;
                                    xp_formula = 'New rating × 8';
                                    break;
                                default: throw new Error("Unsupported trait_type: ".concat(trait_type));
                            }
                            return [2 /*return*/, {
                                    content: [{
                                            type: 'text',
                                            text: "To improve ".concat(trait_name_2, " from ").concat(curr_rating, " to ").concat(new_rating, " costs ").concat(xp_cost, " XP. (").concat(char.name, " has ").concat(char.experience, " XP, rule: ").concat(xp_formula, ")")
                                        },
                                        {
                                            type: 'text',
                                            text: JSON.stringify({
                                                char_name: char.name,
                                                xp_cost: xp_cost,
                                                previous_rating: curr_rating,
                                                new_rating: new_rating,
                                                trait_type: trait_type,
                                                trait_name: trait_name_2,
                                                rule: xp_formula,
                                                current_xp: char.experience
                                            })
                                        }]
                                }];
                        }
                        catch (e) {
                            return [2 /*return*/, { content: [{ type: 'text', text: "\u274C XP cost calculation failed: ".concat(e.message) }], isError: true }];
                        }
                    }
                    default:
                        throw new Error("Unknown tool: ".concat(name));
                }
            }
            catch (error) {
                return [2 /*return*/, {
                        content: makeTextContentArray(["Error in tool '".concat(name, "': ").concat(error.message)]),
                        isError: true
                    }];
            }
            return [2 /*return*/];
        });
    });
}
server.setRequestHandler(types_js_1.CallToolRequestSchema, handleToolRequest);
server.connect(new stdio_js_1.StdioServerTransport());
console.error('oWoD RPG Game State MCP Server v2.1.0 running on stdio');
