# Outstanding and Missing Tests (To-Do)

This document lists all MCP tool tests from the official testing plan that have not been completed, were missing in the last report, or require improved/expanded coverage to reach full compliance and robustness. Each item references the original plan for clarity and test implementation.

---

## Game-State Server

### Character & Antagonist Management

- **`create_character` & `create_antagonist`**
  - [ ] **Antagonist: Splat-Specific Data**
      - Verify joined/persisted data and returned fields by splat on antagonists.

- **`get_antagonist`**
  - [ ] **All retrieval, update, and list tests**
      - No evidence of antagonist-specific retrieval, update, or listing tests in report.

- **`update_antagonist`**
  - [ ] **Standard/Splat-specific/Validation tests**
      - Run updates on antagonists, including splat & type validation.

- **`list_antagonists`**
  - [ ] **Non-empty roster**
      - Only empty test covered; verify listing when antagonists exist.

### Resource & Health

- **`restore_resource`**
  - [ ] **Standard & edge case validation**
      - Explicit “restore_resource” tests not documented.

- **`apply_damage`**
  - [ ] **ALL multi-type and overflow tests**
      - Damage track logic (bashing → lethal, overflow, incapacitation) not tested.
  - [ ] **Integration with subsequent mechanics**
      - Confirm wound penalties applied and influence dice rolls.

### Progression (XP)

- **`spend_xp`**
  - [ ] **Spending XP directly**
      - No explicit test documented.
- **`improve_trait`**
  - [ ] **Validation: Invalid Trait**
      - Case for non-existent trait improvements requires distinct testing.

### Status Effects

- **`remove_status_effect`**
  - [ ] **Test all remove logic**
      - Only apply/retrieve tested; removal is not covered.
- **`get_status_effects`**
  - [ ] **After successful application**
      - The test shows effects not persisting; confirm persistence/association post-fix.

### Inventory

- **`add_item` / `update_item` / `remove_item`**
  - [ ] **ALL inventory operations except add**
      - Only `add_item` was attempted—and failed. Must implement get, update, remove, and confirm correct associations and persistence.

### Persistence

- **`save_world_state`, `get_world_state`, `save_story_progress`**
  - [ ] **All happy/sad path and negative validation**
      - Missing tests for failure/invalid input, serialization/restore edge cases.

---

## Combat-Engine Server

### Initiative & Turn Management

- **`roll_initiative_for_scene`, `set_initiative`, `get_initiative_order`, `advance_turn`, `get_current_turn`**
  - [ ] **All function, integration, and error cases**
      - Serialization/contract bugs block all but basic input tests; actual game flow and validation not covered.

### Game-Line Specific Mechanics

- **`roll_virtue_check` (Vampire)**
  - [ ] **All virtue, frenzy, Rötschreck tests**
      - Serialization error prevented any meaningful validation/test of outputs.

- **`change_form`, `spend_rage_for_extra_actions` (Werewolf)**
  - [ ] **Attribute modifier and rage action tests**
      - Form/rage logic not verified due to contract failures.

- **`roll_magick_effect` (Mage)**
  - [ ] **Coincidental, vulgar, and paradox backlash**
      - No tests succeeded; need to distinct test output/narratives and paradox triggers.

- **`invoke_cantrip` (Changeling)**
  - [ ] **Art + Realm Pool and Banality trigger**
      - Not performed, blocked by serialization bugs.

### Social Combat

- **`roll_social_combat`**
  - [ ] **Intimidation, persuasion, and edge cases**
      - No successful test or output; all social combat scenarios need full coverage.

---

## Coverage and Methodological Gaps

- [ ] **Validation: Invalid Type/Field handling** (all tools)
- [ ] **Full edge case coverage for every tool**
- [ ] **Automated, repeatable regression scenarios for all above**

---

This to-do list should be updated as new features emerge and bugs are resolved. For each item: define concrete test cases, expected outcomes, and include tests for negative/invalid scenarios.