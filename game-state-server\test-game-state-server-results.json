[{"step": 1, "tool": "create_character", "input": {"name": "<PERSON>", "game_line": "vampire", "concept": "Artist", "clan": "<PERSON><PERSON><PERSON>", "strength": 2, "dexterity": 2, "stamina": 2, "charisma": 3, "manipulation": 2, "appearance": 3, "perception": 2, "intelligence": 2, "wits": 2, "willpower_current": 5, "willpower_permanent": 5, "blood_pool_current": 10, "blood_pool_max": 10, "humanity": 7}, "expected": "Character/NPC created and retrievable. Splat-specific tables populated.", "actual": "Error in tool 'create_character': UNIQUE constraint failed: characters.name", "pass": false}, {"step": 2, "tool": "create_character", "input": {"name": "<PERSON>", "game_line": "werewolf", "concept": "Warden", "tribe": "Children of Gaia", "auspice": "<PERSON><PERSON><PERSON>", "strength": 3, "dexterity": 3, "stamina": 3, "charisma": 2, "manipulation": 2, "appearance": 2, "perception": 2, "intelligence": 2, "wits": 2, "willpower_current": 4, "willpower_permanent": 4, "gnosis_current": 3, "gnosis_permanent": 3, "rage": 2}, "expected": "Character created with required fields/valid defaults for all omitted.", "actual": "{\"type\":\"text\",\"text\":\"🎲 World of Darkness: WEREWOLF Sheet\\n\\n👤 Name: Elsa\\n🧠 Concept: Warden\\n🗂️  Game Line: Werewolf\\n💪 Strength: 3\\n🏃 Dexterity: 3\\n❤️ Stamina: 3\\n🎭 Charisma: 2\\n🗣️ Manipulation: 2\\n🌟 Appearance: 2\\n👁️ Perception: 2\\n🧠 Intelligence: 2\\n⚡ Wits: 2\\n────────────── ABILITIES ──────────────\\n  (none recorded)\\n🎲 Most-Used Dice Pools:\\n  - Perception + Alertness: 2\\n  - Dexterity + Brawl: 3\\n  - Manipulation + Subterfuge: 2\\n  - Wits + Intimidation: 2\\n  - Dexterity + Firearms: 3\\n─────────────────────────────────────────────\\n\\n────────────── CORE TRAITS ──────────────\\n🔵 Willpower: 4/4\\nRage: 0, Gnosis: 3, Renown: Glory 0, <PERSON> 0, <PERSON> 0\\n\"}", "pass": true}, {"step": 3, "tool": "create_character", "input": {"game_line": "mage"}, "expected": "Error message: Missing required field: name.", "actual": "Error in tool 'create_character': Missing named parameter \"name\"", "pass": false}, {"step": 4, "tool": "create_character", "input": {"name": "Test", "game_line": "dragon"}, "expected": "Error message: Invalid value for game_line.", "actual": "Error in tool 'create_character': Missing named parameter \"concept\"", "pass": false}, {"step": 5, "tool": "create_character", "input": {"name": "<PERSON>", "game_line": "vampire"}, "expected": "UNIQUE constraint failed error on second attempt.", "actual": "Error in tool 'create_character': Missing named parameter \"concept\"", "pass": false}, {"step": 6, "tool": "get_character", "input": {}, "expected": "Full, correctly formatted character sheet is returned.", "actual": "❌ Character not found!", "pass": false}, {"step": 7, "tool": "get_character_by_name", "input": {"name": "<PERSON>"}, "expected": "Full, correctly formatted character sheet is returned.", "actual": "{\"type\":\"text\",\"text\":\"🎲 World of Darkness: VAMPIRE Sheet\\n\\n👤 Name: <PERSON>\\n🧠 Concept: Artist\\n🗂️  Game Line: Vampire\\n💪 Strength: 2\\n🏃 Dexterity: 2\\n❤️ Stamina: 2\\n🎭 Charisma: 3\\n🗣️ Manipulation: 2\\n🌟 Appearance: 3\\n👁️ Perception: 2\\n🧠 Intelligence: 2\\n⚡ Wits: 2\\n────────────── ABILITIES ──────────────\\n  (none recorded)\\n🎲 Most-Used Dice Pools:\\n  - Perception + Alertness: 2\\n  - Dexterity + Brawl: 2\\n  - Manipulation + Subterfuge: 2\\n  - Wits + Intimidation: 2\\n  - Dexterity + Firearms: 2\\n─────────────────────────────────────────────\\n\\n────────────── CORE TRAITS ──────────────\\n🔵 Willpower: 4/5\\nBlood Pool: 10/10, Humanity: 7\\n\"}", "pass": true}, {"step": 8, "tool": "get_character", "input": {}, "expected": "Response includes Rage, Gnosis, Gifts, etc.", "actual": "❌ Character not found!", "pass": false}, {"step": 9, "tool": "get_character", "input": {"character_id": 99999}, "expected": "Clear \"Not Found\" error message.", "actual": "❌ Character not found!", "pass": false}, {"step": 10, "tool": "get_character", "input": {"character_id": "abc"}, "expected": "Input validation error.", "actual": "❌ Character not found!", "pass": false}, {"step": 11, "tool": "update_character", "input": {"updates": {"concept": "Survivor"}}, "expected": "Success confirmation. get_character reflects the change.", "actual": "✅ Character #undefined updated.", "pass": true}, {"step": 12, "tool": "update_character", "input": {"updates": {"humanity": 6}}, "expected": "Success confirmation. get_character shows new humanity.", "actual": "Error in tool 'update_character': no such column: humanity", "pass": false}, {"step": 13, "tool": "update_character", "input": {"updates": {"luck_points": 5}}, "expected": "Error message: Invalid field 'luck_points'.", "actual": "✅ Character #undefined updated.", "pass": true}, {"step": 14, "tool": "update_character", "input": {"updates": {"strength": "strong"}}, "expected": "Input validation error.", "actual": "✅ Character #undefined updated.", "pass": true}, {"step": 15, "tool": "spend_resource", "input": {"resource_name": "willpower", "amount": 1}, "expected": "Success, new/max values.", "actual": "❌ Character not found!", "pass": false}, {"step": 16, "tool": "restore_resource", "input": {"resource_name": "willpower", "amount": 1}, "expected": "Restores/caps at max.", "actual": "❌ Character not found!", "pass": false}, {"step": 17, "tool": "spend_resource", "input": {"resource_name": "willpower", "amount": 99}, "expected": "Error: Not enough willpower.", "actual": "❌ Character not found!", "pass": false}, {"step": 18, "tool": "restore_resource", "input": {"resource_name": "willpower", "amount": 99}, "expected": "Capped at maximum.", "actual": "❌ Character not found!", "pass": false}, {"step": 19, "tool": "spend_resource", "input": {"resource_name": "gnosis", "amount": 1}, "expected": "Error: Invalid resource for game_line.", "actual": "❌ Character not found!", "pass": false}, {"step": 20, "tool": "gain_resource", "input": {"resource_name": "blood", "roll_successes": 3}, "expected": "Success; blood pool increases.", "actual": "❌ Character not found!", "pass": false}, {"step": 21, "tool": "gain_resource", "input": {"resource_name": "gnosis", "roll_successes": 2}, "expected": "Error; not applicable to Vampire.", "actual": "❌ Character not found!", "pass": false}, {"step": 22, "tool": "gain_resource", "input": {"resource_name": "blood", "roll_successes": 0}, "expected": "Error: roll_successes must be positive.", "actual": "❌ Character not found!", "pass": false}, {"step": 23, "tool": "apply_damage", "input": {"target_type": "character", "damage_successes": 2, "damage_type": "bashing"}, "expected": "<PERSON><PERSON> applied.", "actual": "❌ character not found", "pass": false}, {"step": 24, "tool": "apply_damage", "input": {"target_type": "character", "damage_successes": 1, "damage_type": "lethal"}, "expected": "Bashing upgrades to lethal.", "actual": "❌ character not found", "pass": false}, {"step": 25, "tool": "apply_damage", "input": {"target_type": "character", "damage_successes": 8, "damage_type": "lethal"}, "expected": "Track is full; Incapacitated.", "actual": "❌ character not found", "pass": false}, {"step": 26, "tool": "award_xp", "input": {"amount": 5, "reason": "Story completion"}, "expected": "XP awarded, can be checked.", "actual": "❌ Character not found!", "pass": false}, {"step": 27, "tool": "get_trait_improvement_cost", "input": {"trait_type": "attribute", "trait_name": "strength"}, "expected": "Correct cost for attribute.", "actual": "❌ Character not found!", "pass": false}, {"step": 28, "tool": "improve_trait", "input": {"trait_type": "attribute", "trait_name": "strength"}, "expected": "Trait improved, XP deducted.", "actual": "❌ Character not found!", "pass": false}, {"step": 29, "tool": "spend_xp", "input": {"amount": 99, "reason": "Overspend", "trait_name": "strength"}, "expected": "Error: Not enough XP.", "actual": "❌ Character not found!", "pass": false}, {"step": 30, "tool": "improve_trait", "input": {"trait_type": "attribute", "trait_name": "fake_trait"}, "expected": "Error: <PERSON><PERSON><PERSON> not found.", "actual": "❌ Character not found!", "pass": false}, {"step": 31, "tool": "apply_status_effect", "input": {"target_type": "character", "effect_name": "Cursed"}, "expected": "Status effect applied.", "actual": "🌀 Status effect 'Cursed' applied to character #undefined (ID: 4)\n{\"effect_id\":4,\"target_type\":\"character\",\"effect_name\":\"Cursed\",\"duration_type\":\"indefinite\",\"duration_value\":null}", "pass": true}, {"step": 32, "tool": "remove_status_effect", "input": {"effect_id": 4}, "expected": "Status effect removed.", "actual": "✅ Status effect 4 removed.", "pass": true}, {"step": 33, "tool": "get_status_effects", "input": {"target_type": "character"}, "expected": "Returns all status effects.", "actual": "{\"target_type\":\"character\",\"effects\":[]}", "pass": true}, {"step": 34, "tool": "add_item", "input": {"item": {"item_name": "<PERSON><PERSON>", "quantity": 1}}, "expected": "<PERSON><PERSON> added.", "actual": "Error in tool 'add_item': NOT NULL constraint failed: inventory.character_id", "pass": false}, {"step": 35, "tool": "get_inventory", "input": {}, "expected": "Should list the Katana.", "actual": "🎒 Inventory for Character #undefined:\n  (Empty)", "pass": true}, {"step": 36, "tool": "update_item", "input": {"updates": {"quantity": 2}}, "expected": "Item updated.", "actual": "✅ Item #undefined updated.", "pass": true}, {"step": 37, "tool": "remove_item", "input": {}, "expected": "Item removed.", "actual": "❌ Item not found.", "pass": true}, {"step": 38, "tool": "save_world_state", "input": {"location": "Paris", "notes": "XP Test", "data": {"foo": 123}}, "expected": "World state saved.", "actual": "🌍 World state saved successfully.", "pass": true}, {"step": 39, "tool": "get_world_state", "input": {}, "expected": "World state retrieved.", "actual": "{\n  \"id\": 1,\n  \"location\": \"Paris\",\n  \"notes\": \"XP Test\",\n  \"data\": {\n    \"foo\": 123\n  },\n  \"last_updated\": \"2025-07-09 04:19:54\"\n}", "pass": true}, {"step": 40, "tool": "save_story_progress", "input": {"chapter": "1", "scene": "intro", "summary": "<PERSON><PERSON>"}, "expected": "Story checkpoint saved.", "actual": "📖 Story progress logged for Chapter 1.", "pass": true}, {"step": 41, "tool": "create_antagonist", "input": {"template_name": "<PERSON><PERSON><PERSON>", "custom_name": "<PERSON><PERSON>", "concept": "Bru<PERSON>", "strength": 3, "dexterity": 2, "stamina": 3, "willpower_current": 3, "willpower_permanent": 3}, "expected": "Antagonist created.", "actual": "Error in tool 'create_antagonist': Missing named parameter \"strength\"", "pass": false}, {"step": 42, "tool": "get_antagonist", "input": {}, "expected": "Antagonist retrieved.", "actual": "❌ Antagonist not found.", "pass": false}, {"step": 43, "tool": "update_antagonist", "input": {"updates": {"strength": 5}}, "expected": "Antagonist updated.", "actual": "✅ Antagonist #undefined updated.", "pass": true}, {"step": 44, "tool": "list_antagonists", "input": {}, "expected": "Roster of antagonists.", "actual": "👥 Antagonist Roster:\n  (None)", "pass": true}, {"step": 45, "tool": "remove_antagonist", "input": {}, "expected": "Antagonist removed.", "actual": "❌ Antagonist not found.", "pass": true}, {"step": 46, "tool": "list_characters", "input": {}, "expected": "Roster of all characters.", "actual": "🎭 Character Roster:\n- <PERSON> (vampire) [ID: 4]\n- <PERSON> (werewolf) [ID: 5]\n- MCP_TEST_CHAR (vampire) [ID: 2]\n- Test <PERSON><PERSON><PERSON><PERSON> (vampire) [ID: 1]\n- TestSubject-StatusEffects-1 (vampire) [ID: 3]", "pass": true}, {"step": 47, "tool": "set_initiative", "input": {"scene_id": "scene-1", "entries": [{"actor_name": "<PERSON>", "initiative_score": 10, "turn_order": 1}, {"npc_id": 1, "actor_name": "Demon", "initiative_score": 5, "turn_order": 2}]}, "expected": "Initiative set with PC+NPC.", "actual": "Error in tool 'set_initiative': FOREIGN KEY constraint failed", "pass": false}, {"step": 48, "tool": "get_initiative_order", "input": {"scene_id": "scene-1"}, "expected": "Order returned.", "actual": "{\"scene_id\":\"scene-1\",\"initiative_order\":[]}", "pass": true}, {"step": 49, "tool": "advance_turn", "input": {"scene_id": "scene-1"}, "expected": "Advances the turn.", "actual": "❌ Initiative order not set for this scene.", "pass": false}, {"step": 50, "tool": "get_current_turn", "input": {"scene_id": "scene-1"}, "expected": "Gets actor/round for turn.", "actual": "❌ No initiative order set for this scene.", "pass": false}]