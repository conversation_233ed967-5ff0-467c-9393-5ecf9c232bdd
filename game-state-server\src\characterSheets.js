"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatVampireSheet = formatVampireSheet;
exports.formatWerewolfSheet = formatWerewolfSheet;
exports.formatMageSheet = formatMageSheet;
exports.formatChangelingSheet = formatChangelingSheet;
exports.formatGenericWoDSheet = formatGenericWoDSheet;
exports.formatSheetByGameLine = formatSheetByGameLine;
/**
 * Utility to format derangements/status/XP blocks for all sheets.
 */
function formatStatusBlocks(_a) {
    var _b = _a.derangements, derangements = _b === void 0 ? [] : _b, _c = _a.conditions, conditions = _c === void 0 ? [] : _c, _d = _a.xpHistory, xpHistory = _d === void 0 ? [] : _d;
    var blocks = '';
    // Mental State / Derangements
    if (derangements.length) {
        blocks += "\uD83E\uDDE0 Mental State / Derangements:\n";
        derangements.forEach(function (d) {
            blocks += "  - ".concat(d.derangement).concat(d.description ? ": ".concat(d.description) : '', "\n");
        });
    }
    // Conditions/Status Effects
    if (conditions.length) {
        blocks += "\uD83E\uDDA0 Conditions / Status Effects:\n";
        conditions.forEach(function (c) {
            blocks += "  - ".concat(c.condition_name);
            if (c.duration !== null && c.duration !== undefined)
                blocks += " [".concat(c.duration, " rounds left]");
            if (c.effect_json)
                blocks += ": ".concat(typeof c.effect_json === 'object' ? JSON.stringify(c.effect_json) : c.effect_json);
            blocks += "\n";
        });
    }
    // XP History (if any)
    if (xpHistory.length) {
        blocks += "\uD83D\uDCC8 XP History (last ".concat(xpHistory.length, "):\n");
        xpHistory.forEach(function (xp) {
            blocks += "  - ".concat(xp.amount > 0 ? '+' : '').concat(xp.amount, " XP: ").concat(xp.reason || '', " (").concat(xp.timestamp ? new Date(xp.timestamp).toLocaleDateString() : '', ")\n");
        });
    }
    return blocks;
}
/** Fallback: All WoD lines share these core blocks */
function formatCoreBlocks(character) {
    var _a, _b, _c, _d;
    // Helper: lookup ability rating by case-insensitive name
    function getAbilityRating(abilities, name) {
        if (!Array.isArray(abilities))
            return 0;
        var found = abilities.find(function (ab) { return typeof ab.ability_name === "string" && ab.ability_name.toLowerCase() === name.toLowerCase(); });
        return found ? Number(found.rating) || 0 : 0;
    }
    // COMMON DICE POOLS for Vampire
    function formatCommonDicePools(character) {
        var abilities = character.abilities || [];
        // For Vampire/oWoD, most frequent pools:
        var pools = [
            {
                label: "Perception + Alertness",
                total: Number(character.perception || 0) +
                    getAbilityRating(abilities, "Alertness"),
            },
            {
                label: "Dexterity + Brawl",
                total: Number(character.dexterity || 0) +
                    getAbilityRating(abilities, "Brawl"),
            },
            {
                label: "Manipulation + Subterfuge",
                total: Number(character.manipulation || 0) +
                    getAbilityRating(abilities, "Subterfuge"),
            },
            // Add more as needed (optional):
            {
                label: "Wits + Intimidation",
                total: Number(character.wits || 0) +
                    getAbilityRating(abilities, "Intimidation"),
            },
            {
                label: "Dexterity + Firearms",
                total: Number(character.dexterity || 0) +
                    getAbilityRating(abilities, "Firearms"),
            },
        ];
        // Only show pools where at least one component is nonzero or ability is present
        var filtered = pools.filter(function (p) { return p.total > 0; });
        if (filtered.length === 0)
            return "";
        var block = "🎲 Most-Used Dice Pools:\n";
        block += filtered
            .map(function (p) { return "  - ".concat(p.label, ": ").concat(p.total); })
            .join("\n");
        return block + "\n─────────────────────────────────────────────\n";
    }
    // HEALTH using HealthTracker for graphic block
    var healthBlock = '';
    try {
        // Lazy import to avoid circular dependency (if any)
        var HealthTracker = require('./health-tracker.js').HealthTracker;
        var tracker = HealthTracker.from(character.health_levels);
        var healthBoxes = tracker.getBoxArray(); // Array of "", "/", "X", "*", or custom symbols per wound
        var woundPenalty = tracker.getWoundPenalty();
        healthBlock = '❤️ Health Levels:\n';
        healthBlock += "  [".concat(healthBoxes.map(function (b) { return b ? b : ' '; }).join(']['), "] (Penalty: ").concat(woundPenalty, ")\n");
    }
    catch (e) {
        // fallback (should never trigger)
        healthBlock = '';
    }
    return [
        "\uD83D\uDC64 Name: ".concat(character.name),
        character.concept ? "\uD83E\uDDE0 Concept: ".concat(character.concept) : '',
        "\uD83D\uDDC2\uFE0F  Game Line: ".concat(((_b = (_a = character.game_line) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.toUpperCase()) + ((_c = character.game_line) === null || _c === void 0 ? void 0 : _c.slice(1))),
        '',
        "\uD83D\uDCAA Strength: ".concat(character.strength, "\n\uD83C\uDFC3 Dexterity: ").concat(character.dexterity, "\n\u2764\uFE0F Stamina: ").concat(character.stamina),
        "\uD83C\uDFAD Charisma: ".concat(character.charisma, "\n\uD83D\uDDE3\uFE0F Manipulation: ").concat(character.manipulation, "\n\uD83C\uDF1F Appearance: ").concat(character.appearance),
        "\uD83D\uDC41\uFE0F Perception: ".concat(character.perception, "\n\uD83E\uDDE0 Intelligence: ").concat(character.intelligence, "\n\u26A1 Wits: ").concat(character.wits),
        '',
        '────────────── ABILITIES ──────────────',
        ((_d = character.abilities) === null || _d === void 0 ? void 0 : _d.length)
            ? character.abilities.map(function (ab) { return "  - ".concat(ab.ability_type, ": ").concat(ab.ability_name, " (").concat(ab.rating).concat(ab.specialty ? ", ".concat(ab.specialty) : '', ")"); }).join('\n')
            : '  (none recorded)',
        '',
        formatCommonDicePools(character),
        healthBlock,
        '────────────── CORE TRAITS ──────────────',
        "\uD83D\uDD35 Willpower: ".concat(character.willpower_current, "/").concat(character.willpower_permanent),
        character.power_stat_name && character.power_stat_rating !== undefined
            ? "\uD83E\uDE84 ".concat(character.power_stat_name, ": ").concat(character.power_stat_rating) : ''
    ].filter(Boolean).join('\n');
}
/**
 * Vampire: Adds Disciplines, Blood Pool, Humanity
 */
function formatVampireSheet(opts) {
    var _a, _b;
    var character = opts.character, _c = opts.extra, extra = _c === void 0 ? {} : _c;
    var out = "\uD83C\uDFB2 World of Darkness: VAMPIRE Sheet\n\n";
    out += formatCoreBlocks(character) + '\n';
    out += formatStatusBlocks(opts);
    // Health
    // (health block now included in formatCoreBlocks)
    // Disciplines, Blood Pool, Humanity
    if ((_a = extra.disciplines) === null || _a === void 0 ? void 0 : _a.length) {
        out += "\n🩸 Disciplines:\n";
        extra.disciplines.forEach(function (d) {
            out += "  - ".concat(d.discipline_name, ": ").concat(d.rating, "\n");
        });
    }
    out += "Blood Pool: ".concat(character.blood_pool_current || 0, "/").concat(character.blood_pool_max || 0, ", Humanity: ").concat((_b = character.humanity) !== null && _b !== void 0 ? _b : '', "\n");
    return { type: 'text', text: out };
}
/**
 * Werewolf: Adds Gifts, Rage, Gnosis, Renown
 */
function formatWerewolfSheet(opts) {
    var _a;
    var character = opts.character, _b = opts.extra, extra = _b === void 0 ? {} : _b;
    var out = "\uD83C\uDFB2 World of Darkness: WEREWOLF Sheet\n\n";
    out += formatCoreBlocks(character) + '\n';
    out += formatStatusBlocks(opts);
    // Health
    // (health block now included in formatCoreBlocks)
    // Gifts, Rage, Gnosis, Renown
    if ((_a = extra.gifts) === null || _a === void 0 ? void 0 : _a.length) {
        out += "\n🐺 Gifts:\n";
        extra.gifts.forEach(function (g) {
            out += "  - ".concat(g.gift_name, " (Rank ").concat(g.rank, ")\n");
        });
    }
    out += "Rage: ".concat(character.rage_current || 0, ", Gnosis: ").concat(character.gnosis_current || 0, ", Renown: Glory ").concat(character.renown_glory || 0, ", Honor ").concat(character.renown_honor || 0, ", Wisdom ").concat(character.renown_wisdom || 0, "\n");
    return { type: 'text', text: out };
}
/**
 * Mage: Adds Spheres, Arete, Quintessence, Paradox
 */
function formatMageSheet(opts) {
    var _a;
    var character = opts.character, _b = opts.extra, extra = _b === void 0 ? {} : _b;
    var out = "\uD83C\uDFB2 World of Darkness: MAGE Sheet\n\n";
    out += formatCoreBlocks(character) + '\n';
    out += formatStatusBlocks(opts);
    // Health
    // (health block now included in formatCoreBlocks)
    // Spheres, Arete, Quintessence, Paradox
    if ((_a = extra.spheres) === null || _a === void 0 ? void 0 : _a.length) {
        out += "\n🕯️ Spheres:\n";
        extra.spheres.forEach(function (s) {
            out += "  - ".concat(s.sphere_name, ": ").concat(s.rating, "\n");
        });
    }
    out += "Arete: ".concat(character.arete || 0, ", Quintessence: ").concat(character.quintessence || 0, ", Paradox: ").concat(character.paradox || 0, "\n");
    return { type: 'text', text: out };
}
/**
 * Changeling: Adds Arts, Realms, Glamour, Banality
 */
function formatChangelingSheet(opts) {
    var _a, _b;
    var character = opts.character, _c = opts.extra, extra = _c === void 0 ? {} : _c;
    var out = "\uD83C\uDFB2 World of Darkness: CHANGELING Sheet\n\n";
    out += formatCoreBlocks(character) + '\n';
    out += formatStatusBlocks(opts);
    // Health
    // (health block now included in formatCoreBlocks)
    if ((_a = extra.arts) === null || _a === void 0 ? void 0 : _a.length) {
        out += "\n✨ Arts:\n";
        extra.arts.forEach(function (a) {
            out += "  - ".concat(a.art_name, ": ").concat(a.rating, "\n");
        });
    }
    if ((_b = extra.realms) === null || _b === void 0 ? void 0 : _b.length) {
        out += "🌐 Realms:\n";
        extra.realms.forEach(function (r) {
            out += "  - ".concat(r.realm_name, ": ").concat(r.rating, "\n");
        });
    }
    out += "Glamour: ".concat(character.glamour_current || 0, "/").concat(character.glamour_permanent || 0, ", Banality: ").concat(character.banality_permanent || 0, "\n");
    return { type: 'text', text: out };
}
/**
 * Fallback: Core WoD sheet structure
 */
function formatGenericWoDSheet(opts) {
    var character = opts.character;
    var out = "\uD83C\uDFB2 World of Darkness Character Sheet (Generic)\n\n";
    out += formatCoreBlocks(character) + '\n';
    out += formatStatusBlocks(opts);
    // Health
    // (health block now included in formatCoreBlocks)
    // Power stat if present
    if (character.power_stat_name && character.power_stat_rating !== undefined) {
        out += "".concat(character.power_stat_name, ": ").concat(character.power_stat_rating, "\n");
    }
    return { type: 'text', text: out };
}
/**
 * Selector for formatter function (UI/readability extensibility point)
 */
function formatSheetByGameLine(opts) {
    switch ((opts.character.game_line || '').toLowerCase()) {
        case 'vampire': return formatVampireSheet(opts);
        case 'werewolf': return formatWerewolfSheet(opts);
        case 'mage': return formatMageSheet(opts);
        case 'changeling': return formatChangelingSheet(opts);
        default: return formatGenericWoDSheet(opts);
    }
}
/**
 * To extend for a new game line:
 * 1. Write `function formatHunterSheet(opts: CharacterSheetOptions) {...}`
 * 2. Add `case 'hunter': return formatHunterSheet(opts);` to formatSheetByGameLine
 * 3. (Optionally) update docs/UI layer
 */ 
