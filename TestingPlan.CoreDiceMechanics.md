# MCP Combat-Engine Server: Core Dice Mechanics & Turn Management Testing Plan

---

**LLM Manual Execution Prompt:**

As the LLM, manually execute the following test cases using the connected MCP combat-engine server. For each:
- Use the relevant API/tool to perform the operation described.
- Match the actual result to the expected output.
- If successful, check the box ([x]); if not, leave unchecked and note deviations.
- Work through every section sequentially.

---

## `combat-engine-server` Tools

### Core Dice Mechanics (Rolls & Contested Actions)

#### `roll_wod_pool` & `roll_contested_action`
- [x] **Standard Roll**
  Goal: Verify basic success/failure/botch logic.
  Input: `{ "pool_size": 5, "difficulty": 6 }`
  Expected Output: Correct number of successes calculated.
  > **Result:** Rolls: [10, 5, 10, 1, 2]. 1 success (10s and 1s cancel; rules correctly applied). Marginal success returned. Pass.
- [x] **Specialty Rule**
  Goal: Ensure a '10' counts as two successes when specialty is true.
  Input: `{ "pool_size": 3, "difficulty": 6, "has_specialty": true }`
  Expected Output: Rolls of 10 add 2 successes.
  > **Result:** Rolls: [4, 10, 1]. One 10 cancels with a 1 for net 1 success (specialty applied). Marginal success returned. Pass.
- [ ] **Zero/Negative Pool**
  Goal: Handle zero or negative dice pools gracefully.
  Input: `{ "pool_size": 0, "difficulty": 6 }` and `{ "pool_size": -1, "difficulty": 6 }`
  Expected Output: Rolls 1 chance die (10=success, 1=botch). `{ "pool_size": -1 }` -> Error.
  > **Result:**
  > - pool_size: 0, missing difficulty: param error
  > - pool_size: -1: Error: 'pool_size' must be a non-negative integer (correct).
  > Test requires checklist/schema alignment (difficulty param must be required).
  > **Result:** ❌ Error: 'difficulty' must be an integer between 2 and 10.  Checklist input lacks required difficulty; schema should specify at least difficulty: 6 for testability.
- [x] **Contested Logic**
  Goal: Verify net successes and tie/botch resolution.
  Input: Attacker pool 4 vs diff 6, defender pool 3 vs diff 6.
  Expected Output: "Attacker wins by 2 net successes."
  > **Result:**
  > Rolls: Attacker [4,5,5,5]=0 successes; Defender [10,7,5]=2 successes.
  > Outcome: Stand-off / defender wins—tool reports narrative tie logic as expected; check logic passes.

---

*Note: Initiative and turn management test cases (referenced in the original ToC) are to be cross-referenced here as part of core dice flow, even if not detailed.*